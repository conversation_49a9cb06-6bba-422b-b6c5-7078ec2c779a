"""
Main entry point for the PySide6/VTK GUI application.

This module initializes the Qt application and creates the main window.
"""

import sys
import logging
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QDir
from PySide6.QtGui import QIcon

from .main_window import MainWindow
from .resources.dark_theme import get_stylesheet

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_application() -> QApplication:
    """Set up the Qt application with optimal settings for VTK integration."""
    app = QApplication(sys.argv)

    # VTK optimization: prevent Qt from creating native widget siblings
    # This improves VTK rendering performance
    app.setAttribute(Qt.AA_DontCreateNativeWidgetSiblings)

    # Enable high DPI scaling
    app.setAttribute(Qt.AA_EnableHighDpiScaling)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps)

    # Set application properties
    app.setApplicationName("Pinnacle DICOM Converter")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("WellSpan Radiation Oncology")
    app.setOrganizationDomain("pinnacle-dicom-converter.local")

    # Apply dark theme stylesheet
    app.setStyleSheet(get_stylesheet())
    logger.info("Applied dark theme stylesheet")

    # Set application icon if available
    icon_path = Path(__file__).parent / "resources" / "icons" / "app_icon.png"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))

    return app


def main() -> int:
    """Main entry point for the GUI application.
    
    Returns:
        Exit code (0 for success, non-zero for error)
    """
    try:
        # Create and configure the application
        app = setup_application()
        
        logger.info("Starting Pinnacle DICOM Converter GUI")
        
        # Create and show the main window
        main_window = MainWindow()
        main_window.show()
        
        # Start the event loop
        return app.exec()
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
