"""
Dark theme stylesheet for the Pinnacle DICOM Converter GUI.

Provides a professional dark mode theme with teal accents inspired by
medical imaging software and the NiceGUI implementation.
"""

# Color palette
COLORS = {
    # Main backgrounds
    'bg_primary': '#1e1e1e',      # Main background
    'bg_secondary': '#2d2d2d',     # Secondary panels
    'bg_tertiary': '#3d3d3d',      # Elevated elements
    'bg_input': '#252525',         # Input fields

    # Accents
    'accent_primary': '#4fc3f7',   # Teal accent (similar to reference)
    'accent_hover': '#81d4fa',     # Lighter teal for hover
    'accent_pressed': '#29b6f6',   # Darker teal for pressed

    # Text
    'text_primary': '#e0e0e0',     # Main text
    'text_secondary': '#b0b0b0',   # Secondary text
    'text_disabled': '#707070',    # Disabled text
    'text_accent': '#4fc3f7',      # Accent text

    # Borders
    'border_primary': '#404040',   # Main borders
    'border_focus': '#4fc3f7',     # Focus borders
    'border_disabled': '#2a2a2a',  # Disabled borders

    # Status colors
    'success': '#66bb6a',
    'warning': '#ffa726',
    'error': '#ef5350',
    'info': '#42a5f5',

    # CT viewer specific
    'viewer_bg': '#000000',        # Black for medical images
}


DARK_THEME_STYLESHEET = f"""
/* ===== GLOBAL STYLES ===== */
QWidget {{
    background-color: {COLORS['bg_primary']};
    color: {COLORS['text_primary']};
    font-family: "Segoe UI", Arial, sans-serif;
    font-size: 13px;
}}

/* ===== MAIN WINDOW ===== */
QMainWindow {{
    background-color: {COLORS['bg_primary']};
}}

QMainWindow::separator {{
    background-color: {COLORS['border_primary']};
    width: 1px;
    height: 1px;
}}

/* ===== MENU BAR ===== */
QMenuBar {{
    background-color: {COLORS['bg_secondary']};
    color: {COLORS['text_primary']};
    border-bottom: 1px solid {COLORS['border_primary']};
    padding: 4px;
}}

QMenuBar::item {{
    background-color: transparent;
    padding: 6px 12px;
    border-radius: 4px;
}}

QMenuBar::item:selected {{
    background-color: {COLORS['bg_tertiary']};
}}

QMenuBar::item:pressed {{
    background-color: {COLORS['accent_primary']};
}}

/* ===== MENUS ===== */
QMenu {{
    background-color: {COLORS['bg_secondary']};
    color: {COLORS['text_primary']};
    border: 1px solid {COLORS['border_primary']};
    border-radius: 4px;
    padding: 4px;
}}

QMenu::item {{
    padding: 6px 24px 6px 12px;
    border-radius: 3px;
}}

QMenu::item:selected {{
    background-color: {COLORS['bg_tertiary']};
}}

QMenu::separator {{
    height: 1px;
    background-color: {COLORS['border_primary']};
    margin: 4px 8px;
}}

/* ===== STATUS BAR ===== */
QStatusBar {{
    background-color: {COLORS['bg_secondary']};
    color: {COLORS['text_secondary']};
    border-top: 1px solid {COLORS['border_primary']};
}}

/* ===== DOCK WIDGETS ===== */
QDockWidget {{
    background-color: {COLORS['bg_secondary']};
    color: {COLORS['text_primary']};
    titlebar-close-icon: url(close.png);
    titlebar-normal-icon: url(float.png);
}}

QDockWidget::title {{
    background-color: {COLORS['bg_tertiary']};
    color: {COLORS['text_primary']};
    text-align: left;
    padding: 8px;
    border-bottom: 1px solid {COLORS['border_primary']};
    font-weight: bold;
    font-size: 14px;
}}

QDockWidget::close-button, QDockWidget::float-button {{
    background-color: transparent;
    border: none;
    padding: 2px;
}}

QDockWidget::close-button:hover, QDockWidget::float-button:hover {{
    background-color: {COLORS['bg_primary']};
    border-radius: 3px;
}}

/* ===== LABELS ===== */
QLabel {{
    background-color: transparent;
    color: {COLORS['text_primary']};
}}

QLabel[class="section-header"] {{
    font-size: 14px;
    font-weight: bold;
    color: {COLORS['accent_primary']};
    padding: 8px 0px 4px 0px;
}}

QLabel[class="info-label"] {{
    color: {COLORS['text_secondary']};
    font-size: 12px;
}}

/* ===== BUTTONS ===== */
QPushButton {{
    background-color: {COLORS['bg_tertiary']};
    color: {COLORS['text_primary']};
    border: 1px solid {COLORS['border_primary']};
    border-radius: 4px;
    padding: 8px 16px;
    min-height: 24px;
}}

QPushButton:hover {{
    background-color: {COLORS['accent_hover']};
    border-color: {COLORS['accent_primary']};
    color: {COLORS['bg_primary']};
}}

QPushButton:pressed {{
    background-color: {COLORS['accent_pressed']};
}}

QPushButton:disabled {{
    background-color: {COLORS['bg_secondary']};
    color: {COLORS['text_disabled']};
    border-color: {COLORS['border_disabled']};
}}

QPushButton[class="accent"] {{
    background-color: {COLORS['accent_primary']};
    color: {COLORS['bg_primary']};
    font-weight: bold;
}}

QPushButton[class="accent"]:hover {{
    background-color: {COLORS['accent_hover']};
}}

/* ===== LINE EDITS ===== */
QLineEdit {{
    background-color: {COLORS['bg_input']};
    color: {COLORS['text_primary']};
    border: 1px solid {COLORS['border_primary']};
    border-radius: 4px;
    padding: 6px;
    selection-background-color: {COLORS['accent_primary']};
}}

QLineEdit:focus {{
    border-color: {COLORS['border_focus']};
}}

QLineEdit:disabled {{
    background-color: {COLORS['bg_secondary']};
    color: {COLORS['text_disabled']};
}}

/* ===== COMBO BOXES ===== */
QComboBox {{
    background-color: {COLORS['bg_input']};
    color: {COLORS['text_primary']};
    border: 1px solid {COLORS['border_primary']};
    border-radius: 4px;
    padding: 6px;
    min-height: 24px;
}}

QComboBox:hover {{
    border-color: {COLORS['accent_primary']};
}}

QComboBox:focus {{
    border-color: {COLORS['border_focus']};
}}

QComboBox::drop-down {{
    border: none;
    width: 20px;
}}

QComboBox::down-arrow {{
    image: url(down_arrow.png);
    width: 12px;
    height: 12px;
}}

QComboBox QAbstractItemView {{
    background-color: {COLORS['bg_secondary']};
    color: {COLORS['text_primary']};
    selection-background-color: {COLORS['accent_primary']};
    border: 1px solid {COLORS['border_primary']};
}}

/* ===== SPIN BOXES ===== */
QSpinBox, QDoubleSpinBox {{
    background-color: {COLORS['bg_input']};
    color: {COLORS['text_primary']};
    border: 1px solid {COLORS['border_primary']};
    border-radius: 4px;
    padding: 6px;
}}

QSpinBox:focus, QDoubleSpinBox:focus {{
    border-color: {COLORS['border_focus']};
}}

/* ===== SLIDERS ===== */
QSlider::groove:horizontal {{
    background-color: {COLORS['bg_tertiary']};
    height: 6px;
    border-radius: 3px;
}}

QSlider::handle:horizontal {{
    background-color: {COLORS['accent_primary']};
    width: 16px;
    height: 16px;
    margin: -5px 0;
    border-radius: 8px;
}}

QSlider::handle:horizontal:hover {{
    background-color: {COLORS['accent_hover']};
}}

QSlider::groove:vertical {{
    background-color: {COLORS['bg_tertiary']};
    width: 6px;
    border-radius: 3px;
}}

QSlider::handle:vertical {{
    background-color: {COLORS['accent_primary']};
    width: 16px;
    height: 16px;
    margin: 0 -5px;
    border-radius: 8px;
}}

QSlider::handle:vertical:hover {{
    background-color: {COLORS['accent_hover']};
}}

/* ===== SCROLL BARS ===== */
QScrollBar:vertical {{
    background-color: {COLORS['bg_secondary']};
    width: 12px;
    border-radius: 6px;
}}

QScrollBar::handle:vertical {{
    background-color: {COLORS['bg_tertiary']};
    min-height: 20px;
    border-radius: 6px;
}}

QScrollBar::handle:vertical:hover {{
    background-color: {COLORS['accent_primary']};
}}

QScrollBar:horizontal {{
    background-color: {COLORS['bg_secondary']};
    height: 12px;
    border-radius: 6px;
}}

QScrollBar::handle:horizontal {{
    background-color: {COLORS['bg_tertiary']};
    min-width: 20px;
    border-radius: 6px;
}}

QScrollBar::handle:horizontal:hover {{
    background-color: {COLORS['accent_primary']};
}}

QScrollBar::add-line, QScrollBar::sub-line {{
    background: none;
    border: none;
}}

/* ===== TABLES ===== */
QTableWidget, QTableView {{
    background-color: {COLORS['bg_primary']};
    alternate-background-color: {COLORS['bg_secondary']};
    color: {COLORS['text_primary']};
    gridline-color: {COLORS['border_primary']};
    border: 1px solid {COLORS['border_primary']};
    border-radius: 4px;
}}

QTableWidget::item, QTableView::item {{
    padding: 4px;
}}

QTableWidget::item:selected, QTableView::item:selected {{
    background-color: {COLORS['accent_primary']};
    color: {COLORS['bg_primary']};
}}

QHeaderView::section {{
    background-color: {COLORS['bg_tertiary']};
    color: {COLORS['text_primary']};
    padding: 6px;
    border: none;
    border-right: 1px solid {COLORS['border_primary']};
    border-bottom: 1px solid {COLORS['border_primary']};
    font-weight: bold;
}}

/* ===== TREE WIDGETS ===== */
QTreeWidget, QTreeView {{
    background-color: {COLORS['bg_primary']};
    color: {COLORS['text_primary']};
    border: 1px solid {COLORS['border_primary']};
    border-radius: 4px;
    outline: none;
}}

QTreeWidget::item, QTreeView::item {{
    padding: 4px;
    border-radius: 3px;
}}

QTreeWidget::item:selected, QTreeView::item:selected {{
    background-color: {COLORS['accent_primary']};
    color: {COLORS['bg_primary']};
}}

QTreeWidget::item:hover, QTreeView::item:hover {{
    background-color: {COLORS['bg_tertiary']};
}}

QTreeWidget::branch {{
    background-color: transparent;
}}

/* ===== TAB WIDGETS ===== */
QTabWidget::pane {{
    background-color: {COLORS['bg_secondary']};
    border: 1px solid {COLORS['border_primary']};
    border-radius: 4px;
}}

QTabBar::tab {{
    background-color: {COLORS['bg_tertiary']};
    color: {COLORS['text_secondary']};
    padding: 8px 16px;
    border: 1px solid {COLORS['border_primary']};
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    margin-right: 2px;
}}

QTabBar::tab:selected {{
    background-color: {COLORS['bg_secondary']};
    color: {COLORS['text_primary']};
    border-bottom: 2px solid {COLORS['accent_primary']};
}}

QTabBar::tab:hover {{
    background-color: {COLORS['bg_secondary']};
    color: {COLORS['accent_primary']};
}}

/* ===== GROUP BOXES ===== */
QGroupBox {{
    background-color: {COLORS['bg_secondary']};
    border: 1px solid {COLORS['border_primary']};
    border-radius: 4px;
    margin-top: 12px;
    padding-top: 12px;
}}

QGroupBox::title {{
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 8px;
    color: {COLORS['accent_primary']};
    font-weight: bold;
}}

/* ===== TOOLTIPS ===== */
QToolTip {{
    background-color: {COLORS['bg_tertiary']};
    color: {COLORS['text_primary']};
    border: 1px solid {COLORS['border_primary']};
    border-radius: 4px;
    padding: 4px 8px;
}}

/* ===== PROGRESS BARS ===== */
QProgressBar {{
    background-color: {COLORS['bg_tertiary']};
    color: {COLORS['text_primary']};
    border: 1px solid {COLORS['border_primary']};
    border-radius: 4px;
    text-align: center;
}}

QProgressBar::chunk {{
    background-color: {COLORS['accent_primary']};
    border-radius: 3px;
}}

/* ===== CUSTOM CLASSES ===== */
#centralWidget {{
    background-color: {COLORS['viewer_bg']};
}}

QLabel#placeholderLabel {{
    background-color: {COLORS['bg_secondary']};
    color: {COLORS['text_secondary']};
    border: 2px dashed {COLORS['border_primary']};
    border-radius: 8px;
    padding: 40px;
    font-size: 16px;
}}
"""


def get_stylesheet() -> str:
    """Get the complete dark theme stylesheet.

    Returns:
        Complete stylesheet string
    """
    return DARK_THEME_STYLESHEET


def get_color(name: str) -> str:
    """Get a specific color from the theme palette.

    Args:
        name: Color name from COLORS dictionary

    Returns:
        Hex color code
    """
    return COLORS.get(name, COLORS['text_primary'])
