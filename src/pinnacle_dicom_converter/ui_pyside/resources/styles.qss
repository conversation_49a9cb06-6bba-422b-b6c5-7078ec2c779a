/*
Qt Stylesheet for Pinnacle DICOM Converter GUI

This stylesheet provides a modern, professional appearance
optimized for medical imaging applications.
*/

/* Main Window Styling */
QMainWindow {
    background-color: #f5f5f5;
    color: #333333;
}

/* Dock Widget Styling */
QDockWidget {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    titlebar-close-icon: url(icons/close.png);
    titlebar-normal-icon: url(icons/undock.png);
}

QDockWidget::title {
    background-color: #e8e8e8;
    color: #333333;
    font-weight: bold;
    padding: 5px;
    border-bottom: 1px solid #cccccc;
}

/* Menu Bar Styling */
QMenuBar {
    background-color: #ffffff;
    border-bottom: 1px solid #cccccc;
    padding: 2px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
    margin: 2px;
    border-radius: 3px;
}

QMenuBar::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

QMenuBar::item:pressed {
    background-color: #bbdefb;
}

/* Menu Styling */
QMenu {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 4px;
}

QMenu::item {
    padding: 6px 20px;
    border-radius: 3px;
}

QMenu::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

QMenu::item:disabled {
    color: #999999;
}

QMenu::separator {
    height: 1px;
    background-color: #e0e0e0;
    margin: 4px 0px;
}

/* Status Bar Styling */
QStatusBar {
    background-color: #f8f8f8;
    border-top: 1px solid #e0e0e0;
    color: #666666;
    font-size: 12px;
}

/* Button Styling */
QPushButton {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 13px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #f0f0f0;
    border-color: #999999;
}

QPushButton:pressed {
    background-color: #e0e0e0;
    border-color: #666666;
}

QPushButton:disabled {
    background-color: #f5f5f5;
    color: #999999;
    border-color: #e0e0e0;
}

/* Label Styling */
QLabel {
    color: #333333;
}

/* Placeholder styling for development */
QLabel[objectName="placeholder"] {
    background-color: #f9f9f9;
    border: 2px dashed #cccccc;
    border-radius: 8px;
    padding: 20px;
    color: #666666;
    font-style: italic;
}

/* Central widget styling */
QWidget[objectName="centralWidget"] {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
}
