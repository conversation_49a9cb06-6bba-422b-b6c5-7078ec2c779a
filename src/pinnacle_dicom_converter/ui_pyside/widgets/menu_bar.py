"""
Custom menu bar widget for the Pinnacle DICOM Converter GUI.

This module provides the main application menu bar with file operations,
view controls, and help functionality, adapted from the NiceGUI header toolbar.
"""

import logging
from PySide6.QtWidgets import QMenuBar, QMenu, QMessageBox, QFileDialog
from PySide6.QtCore import QObject, Signal
from PySide6.QtGui import QAction, QKeySequence

logger = logging.getLogger(__name__)


class MenuBarActions(QObject):
    """Signal container for menu bar actions.
    
    This class provides signals that can be connected to ViewModels
    for handling menu actions in an MVVM pattern.
    """
    
    # File menu signals
    open_directory_requested = Signal()
    open_archive_requested = Signal()
    close_dataset_requested = Signal()
    
    # Export menu signals
    save_dicom_local_requested = Signal()
    export_dicom_network_requested = Signal()
    
    # View menu signals
    toggle_left_dock_requested = Signal()
    toggle_right_dock_requested = Signal()
    
    # Help menu signals
    show_help_requested = Signal()
    show_about_requested = Signal()


def create_menu_bar(parent) -> QMenuBar:
    """Create and configure the main menu bar.
    
    Args:
        parent: Parent widget (typically MainWindow)
        
    Returns:
        Configured QMenuBar instance
    """
    menu_bar = QMenuBar(parent)
    
    # Create actions container for signal emission
    actions = MenuBarActions(parent)
    parent.menu_actions = actions  # Store reference for later connection
    
    # File Menu
    file_menu = menu_bar.addMenu("&File")
    
    # Open Directory action
    open_dir_action = QAction("Open &Directory...", parent)
    open_dir_action.setShortcut(QKeySequence("Ctrl+O"))
    open_dir_action.setStatusTip("Open a Pinnacle dataset directory")
    open_dir_action.triggered.connect(lambda: _handle_open_directory(parent, actions))
    file_menu.addAction(open_dir_action)
    
    # Open Archive action
    open_archive_action = QAction("Open &Archive...", parent)
    open_archive_action.setShortcut(QKeySequence("Ctrl+Shift+O"))
    open_archive_action.setStatusTip("Open a Pinnacle dataset archive (.tar, .tar.gz, .zip)")
    open_archive_action.triggered.connect(lambda: _handle_open_archive(parent, actions))
    file_menu.addAction(open_archive_action)
    
    file_menu.addSeparator()
    
    # Close Dataset action
    close_action = QAction("&Close Dataset", parent)
    close_action.setShortcut(QKeySequence("Ctrl+W"))
    close_action.setStatusTip("Close the current dataset")
    close_action.setEnabled(False)  # Initially disabled
    close_action.triggered.connect(actions.close_dataset_requested.emit)
    file_menu.addAction(close_action)
    parent.close_dataset_action = close_action  # Store reference for enabling/disabling
    
    file_menu.addSeparator()
    
    # Exit action
    exit_action = QAction("E&xit", parent)
    exit_action.setShortcut(QKeySequence("Ctrl+Q"))
    exit_action.setStatusTip("Exit the application")
    exit_action.triggered.connect(parent.close)
    file_menu.addAction(exit_action)
    
    # Export Menu
    export_menu = menu_bar.addMenu("&Export")
    
    # Save DICOM Local action
    save_local_action = QAction("&Save DICOM Files...", parent)
    save_local_action.setShortcut(QKeySequence("Ctrl+S"))
    save_local_action.setStatusTip("Save DICOM files to local directory")
    save_local_action.setEnabled(False)  # Initially disabled
    save_local_action.triggered.connect(actions.save_dicom_local_requested.emit)
    export_menu.addAction(save_local_action)
    parent.save_local_action = save_local_action
    
    # Export DICOM Network action
    export_network_action = QAction("Send to &Network...", parent)
    export_network_action.setShortcut(QKeySequence("Ctrl+N"))
    export_network_action.setStatusTip("Send DICOM files to network destination")
    export_network_action.setEnabled(False)  # Initially disabled
    export_network_action.triggered.connect(actions.export_dicom_network_requested.emit)
    export_menu.addAction(export_network_action)
    parent.export_network_action = export_network_action
    
    # View Menu
    view_menu = menu_bar.addMenu("&View")
    
    # Toggle Left Dock action
    toggle_left_action = QAction("Toggle &Navigation Panel", parent)
    toggle_left_action.setShortcut(QKeySequence("F9"))
    toggle_left_action.setStatusTip("Show/hide the navigation panel")
    toggle_left_action.triggered.connect(lambda: parent.left_dock.setVisible(not parent.left_dock.isVisible()))
    view_menu.addAction(toggle_left_action)
    
    # Toggle Right Dock action
    toggle_right_action = QAction("Toggle &Controls Panel", parent)
    toggle_right_action.setShortcut(QKeySequence("F10"))
    toggle_right_action.setStatusTip("Show/hide the controls panel")
    toggle_right_action.triggered.connect(lambda: parent.right_dock.setVisible(not parent.right_dock.isVisible()))
    view_menu.addAction(toggle_right_action)
    
    # Help Menu
    help_menu = menu_bar.addMenu("&Help")
    
    # Keyboard Shortcuts action
    shortcuts_action = QAction("&Keyboard Shortcuts", parent)
    shortcuts_action.setShortcut(QKeySequence("F1"))
    shortcuts_action.setStatusTip("Show keyboard shortcuts")
    shortcuts_action.triggered.connect(lambda: _show_keyboard_shortcuts(parent))
    help_menu.addAction(shortcuts_action)
    
    help_menu.addSeparator()
    
    # About action
    about_action = QAction("&About", parent)
    about_action.setStatusTip("About Pinnacle DICOM Converter")
    about_action.triggered.connect(lambda: _show_about_dialog(parent))
    help_menu.addAction(about_action)
    
    logger.info("Menu bar created with all actions")
    return menu_bar


def _handle_open_directory(parent, actions):
    """Handle open directory action."""
    directory = QFileDialog.getExistingDirectory(
        parent,
        "Select Pinnacle Dataset Directory",
        "",
        QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
    )
    
    if directory:
        logger.info(f"Directory selected: {directory}")
        actions.open_directory_requested.emit()
        # Enable dataset-dependent actions
        parent.close_dataset_action.setEnabled(True)
        parent.save_local_action.setEnabled(True)
        parent.export_network_action.setEnabled(True)


def _handle_open_archive(parent, actions):
    """Handle open archive action."""
    archive_file, _ = QFileDialog.getOpenFileName(
        parent,
        "Select Pinnacle Dataset Archive",
        "",
        "Archive Files (*.tar *.tar.gz *.tgz *.zip);;All Files (*)"
    )
    
    if archive_file:
        logger.info(f"Archive selected: {archive_file}")
        actions.open_archive_requested.emit()
        # Enable dataset-dependent actions
        parent.close_dataset_action.setEnabled(True)
        parent.save_local_action.setEnabled(True)
        parent.export_network_action.setEnabled(True)


def _show_keyboard_shortcuts(parent):
    """Show keyboard shortcuts dialog."""
    shortcuts_text = """
    <h3>Keyboard Shortcuts</h3>
    <table>
    <tr><td><b>Ctrl+O</b></td><td>Open Directory</td></tr>
    <tr><td><b>Ctrl+Shift+O</b></td><td>Open Archive</td></tr>
    <tr><td><b>Ctrl+W</b></td><td>Close Dataset</td></tr>
    <tr><td><b>Ctrl+S</b></td><td>Save DICOM Files</td></tr>
    <tr><td><b>Ctrl+N</b></td><td>Send to Network</td></tr>
    <tr><td><b>F9</b></td><td>Toggle Navigation Panel</td></tr>
    <tr><td><b>F10</b></td><td>Toggle Controls Panel</td></tr>
    <tr><td><b>F1</b></td><td>Show Shortcuts</td></tr>
    <tr><td><b>Ctrl+Q</b></td><td>Exit Application</td></tr>
    </table>
    <p><i>Additional TPS viewer shortcuts will be available when implemented.</i></p>
    """
    
    QMessageBox.information(parent, "Keyboard Shortcuts", shortcuts_text)


def _show_about_dialog(parent):
    """Show about dialog."""
    about_text = """
    <h2>Pinnacle DICOM Converter</h2>
    <p><b>Version:</b> 1.0.0</p>
    <p><b>Description:</b> High-performance TPS viewer for radiotherapy data</p>
    <p><b>Technology:</b> PySide6 + VTK</p>
    <p><b>Purpose:</b> Convert and visualize Pinnacle treatment planning data</p>
    <hr>
    <p><i>Built for medical physicists and radiotherapy departments</i></p>
    """
    
    QMessageBox.about(parent, "About Pinnacle DICOM Converter", about_text)
