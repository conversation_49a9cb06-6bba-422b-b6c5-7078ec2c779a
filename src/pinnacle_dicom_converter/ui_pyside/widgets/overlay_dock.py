"""
Overlay dock widget for ROI/POI/Beam/Dose controls.

This widget provides the right dock interface for managing overlay
visibility and properties using the OverlayViewModel.
"""

import logging
from typing import Optional

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QListView,
    QPushButton, QFrame, QSizePolicy, QCheckBox, QTabWidget,
    QSlider, QSpinBox, QGroupBox
)
from PySide6.QtCore import Qt, QModelIndex

from ..viewmodels.overlay_viewmodel import OverlayViewModel

logger = logging.getLogger(__name__)


class OverlayDock(QWidget):
    """Overlay dock widget for CT/ROI/POI/Beam/Dose controls.

    This widget provides tabbed interface for managing CT image display
    and different types of TPS overlays with visibility and property controls.
    """

    def __init__(self, overlay_viewmodel: OverlayViewModel, tps_viewmodel=None, parent=None):
        """Initialize the overlay dock.

        Args:
            overlay_viewmodel: OverlayViewModel instance
            tps_viewmodel: TPSViewModel instance for CT controls
            parent: Parent widget
        """
        super().__init__(parent)

        self.overlay_viewmodel = overlay_viewmodel
        self.tps_viewmodel = tps_viewmodel
        
        # UI components
        self.tab_widget: Optional[QTabWidget] = None
        self.rois_list: Optional[QListView] = None
        self.pois_list: Optional[QListView] = None
        self.beams_list: Optional[QListView] = None
        
        self.setup_ui()
        self.setup_connections()
        
        logger.info("Overlay dock initialized")
    
    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Tab widget for different overlay types
        self.tab_widget = QTabWidget()

        # CT tab (first tab - primary controls)
        ct_tab = self._create_ct_tab()
        self.tab_widget.addTab(ct_tab, "CT")

        # ROIs tab
        rois_tab = self._create_rois_tab()
        self.tab_widget.addTab(rois_tab, "ROIs")
        
        # POIs tab
        pois_tab = self._create_pois_tab()
        self.tab_widget.addTab(pois_tab, "POIs")
        
        # Beams tab
        beams_tab = self._create_beams_tab()
        self.tab_widget.addTab(beams_tab, "Beams")
        
        # Dose tab
        dose_tab = self._create_dose_tab()
        self.tab_widget.addTab(dose_tab, "Dose")
        
        layout.addWidget(self.tab_widget)
        
        # Status section
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_layout = QVBoxLayout(status_frame)
        
        status_label = QLabel("Overlays")
        status_label.setStyleSheet("font-weight: bold; color: #333333;")
        status_layout.addWidget(status_label)
        
        self.overlay_status_label = QLabel("No overlays loaded")
        self.overlay_status_label.setStyleSheet("color: #666666; font-style: italic;")
        self.overlay_status_label.setWordWrap(True)
        status_layout.addWidget(self.overlay_status_label)
        
        layout.addWidget(status_frame)

    def _create_ct_tab(self) -> QWidget:
        """Create the CT controls tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # Image Information section
        info_group = QGroupBox("Image Information")
        info_layout = QVBoxLayout(info_group)

        self.image_info_label = QLabel("No image loaded")
        self.image_info_label.setStyleSheet("color: #666666; font-size: 11px;")
        self.image_info_label.setWordWrap(True)
        info_layout.addWidget(self.image_info_label)

        layout.addWidget(info_group)

        # Slice Navigation section
        slice_group = QGroupBox("Slice Navigation")
        slice_layout = QVBoxLayout(slice_group)

        # Slice slider and spinbox
        slice_controls_layout = QHBoxLayout()

        self.slice_slider = QSlider(Qt.Horizontal)
        self.slice_slider.setMinimum(0)
        self.slice_slider.setMaximum(0)
        self.slice_slider.setValue(0)
        slice_controls_layout.addWidget(self.slice_slider)

        self.slice_spinbox = QSpinBox()
        self.slice_spinbox.setMinimum(1)  # 1-based for user display
        self.slice_spinbox.setMaximum(1)
        self.slice_spinbox.setValue(1)
        self.slice_spinbox.setFixedWidth(80)
        slice_controls_layout.addWidget(self.slice_spinbox)

        slice_layout.addLayout(slice_controls_layout)

        # Slice info label
        self.slice_info_label = QLabel("Slice: 1 / 1")
        self.slice_info_label.setStyleSheet("color: #666666; font-size: 11px;")
        slice_layout.addWidget(self.slice_info_label)

        layout.addWidget(slice_group)

        # Window Width section
        ww_group = QGroupBox("Window Width")
        ww_layout = QVBoxLayout(ww_group)

        ww_controls_layout = QHBoxLayout()

        self.ww_slider = QSlider(Qt.Horizontal)
        self.ww_slider.setMinimum(1)
        self.ww_slider.setMaximum(4000)
        self.ww_slider.setValue(1400)
        ww_controls_layout.addWidget(self.ww_slider)

        self.ww_spinbox = QSpinBox()
        self.ww_spinbox.setMinimum(1)
        self.ww_spinbox.setMaximum(4000)
        self.ww_spinbox.setValue(1400)
        self.ww_spinbox.setFixedWidth(80)
        ww_controls_layout.addWidget(self.ww_spinbox)

        ww_layout.addLayout(ww_controls_layout)
        layout.addWidget(ww_group)

        # Window Level section
        wl_group = QGroupBox("Window Level")
        wl_layout = QVBoxLayout(wl_group)

        wl_controls_layout = QHBoxLayout()

        self.wl_slider = QSlider(Qt.Horizontal)
        self.wl_slider.setMinimum(-1024)
        self.wl_slider.setMaximum(3071)
        self.wl_slider.setValue(1000)
        wl_controls_layout.addWidget(self.wl_slider)

        self.wl_spinbox = QSpinBox()
        self.wl_spinbox.setMinimum(-1024)
        self.wl_spinbox.setMaximum(3071)
        self.wl_spinbox.setValue(1000)
        self.wl_spinbox.setFixedWidth(80)
        wl_controls_layout.addWidget(self.wl_spinbox)

        wl_layout.addLayout(wl_controls_layout)
        layout.addWidget(wl_group)

        # Zoom section
        zoom_group = QGroupBox("Zoom")
        zoom_layout = QVBoxLayout(zoom_group)

        zoom_controls_layout = QHBoxLayout()

        self.zoom_slider = QSlider(Qt.Horizontal)
        self.zoom_slider.setMinimum(10)  # 0.1x zoom
        self.zoom_slider.setMaximum(1000)  # 10.0x zoom
        self.zoom_slider.setValue(100)  # 1.0x zoom
        zoom_controls_layout.addWidget(self.zoom_slider)

        zoom_reset_btn = QPushButton("Reset")
        zoom_reset_btn.setFixedWidth(60)
        zoom_reset_btn.clicked.connect(self._reset_zoom)
        zoom_controls_layout.addWidget(zoom_reset_btn)

        zoom_layout.addLayout(zoom_controls_layout)

        self.zoom_info_label = QLabel("100%")
        self.zoom_info_label.setStyleSheet("color: #666666; font-size: 11px;")
        zoom_layout.addWidget(self.zoom_info_label)

        layout.addWidget(zoom_group)

        # Add stretch to push everything to top
        layout.addStretch()

        # Connect CT controls to TPS ViewModel if available
        if self.tps_viewmodel:
            self._connect_ct_controls()

        return widget

    def _create_rois_tab(self) -> QWidget:
        """Create the ROIs tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # ROIs list
        self.rois_list = QListView()
        self.rois_list.setModel(self.overlay_viewmodel.rois_model)
        layout.addWidget(self.rois_list)
        
        # ROI controls
        controls_layout = QHBoxLayout()
        
        show_all_btn = QPushButton("Show All")
        show_all_btn.clicked.connect(self._show_all_rois)
        controls_layout.addWidget(show_all_btn)
        
        hide_all_btn = QPushButton("Hide All")
        hide_all_btn.clicked.connect(self._hide_all_rois)
        controls_layout.addWidget(hide_all_btn)
        
        layout.addLayout(controls_layout)
        
        return widget
    
    def _create_pois_tab(self) -> QWidget:
        """Create the POIs tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # POIs list
        self.pois_list = QListView()
        self.pois_list.setModel(self.overlay_viewmodel.pois_model)
        layout.addWidget(self.pois_list)
        
        # POI controls
        controls_layout = QHBoxLayout()
        
        show_all_btn = QPushButton("Show All")
        show_all_btn.clicked.connect(self._show_all_pois)
        controls_layout.addWidget(show_all_btn)
        
        hide_all_btn = QPushButton("Hide All")
        hide_all_btn.clicked.connect(self._hide_all_pois)
        controls_layout.addWidget(hide_all_btn)
        
        layout.addLayout(controls_layout)
        
        return widget
    
    def _create_beams_tab(self) -> QWidget:
        """Create the Beams tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Beams list
        self.beams_list = QListView()
        self.beams_list.setModel(self.overlay_viewmodel.beams_model)
        layout.addWidget(self.beams_list)
        
        # Beam controls
        controls_layout = QHBoxLayout()
        
        show_all_btn = QPushButton("Show All")
        show_all_btn.clicked.connect(self._show_all_beams)
        controls_layout.addWidget(show_all_btn)
        
        hide_all_btn = QPushButton("Hide All")
        hide_all_btn.clicked.connect(self._hide_all_beams)
        controls_layout.addWidget(hide_all_btn)
        
        layout.addLayout(controls_layout)
        
        return widget
    
    def _create_dose_tab(self) -> QWidget:
        """Create the Dose tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Dose controls
        dose_label = QLabel("Isodose Lines")
        dose_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(dose_label)
        
        # Placeholder for dose controls
        dose_info = QLabel("Dose visualization controls will be implemented in Phase 4")
        dose_info.setStyleSheet("color: #666666; font-style: italic;")
        dose_info.setWordWrap(True)
        layout.addWidget(dose_info)
        
        layout.addStretch()
        
        return widget
    
    def setup_connections(self):
        """Set up signal/slot connections."""
        # ViewModel signals
        self.overlay_viewmodel.roisLoaded.connect(self._on_rois_loaded)
        self.overlay_viewmodel.poisLoaded.connect(self._on_pois_loaded)
        self.overlay_viewmodel.beamsLoaded.connect(self._on_beams_loaded)
        self.overlay_viewmodel.doseLoaded.connect(self._on_dose_loaded)
        self.overlay_viewmodel.loadingStateChanged.connect(self._on_loading_state_changed)
        self.overlay_viewmodel.errorOccurred.connect(self._on_error_occurred)

    def _connect_ct_controls(self):
        """Connect CT controls to TPS ViewModel."""
        if not self.tps_viewmodel:
            return

        # Connect slice controls
        self.slice_slider.valueChanged.connect(self.tps_viewmodel.set_slice_index)
        self.slice_spinbox.valueChanged.connect(lambda v: self.tps_viewmodel.set_slice_index(v - 1))  # Convert to 0-based

        # Connect window/level controls
        self.ww_slider.valueChanged.connect(self._on_window_width_changed)
        self.ww_spinbox.valueChanged.connect(self._on_window_width_changed)
        self.wl_slider.valueChanged.connect(self._on_window_level_changed)
        self.wl_spinbox.valueChanged.connect(self._on_window_level_changed)

        # Connect zoom control
        self.zoom_slider.valueChanged.connect(self._on_zoom_changed)

        # Connect TPS ViewModel signals to update UI
        self.tps_viewmodel.imageLoaded.connect(self._on_image_loaded)
        self.tps_viewmodel.sliceChanged.connect(self._on_slice_changed)
        self.tps_viewmodel.windowLevelChanged.connect(self._on_window_level_updated)

    def _on_window_width_changed(self, value):
        """Handle window width changes."""
        if self.tps_viewmodel:
            # Sync slider and spinbox
            if self.sender() == self.ww_slider:
                self.ww_spinbox.setValue(value)
            else:
                self.ww_slider.setValue(value)

            # Update ViewModel
            self.tps_viewmodel.set_window_level(value, self.wl_slider.value())

    def _on_window_level_changed(self, value):
        """Handle window level changes."""
        if self.tps_viewmodel:
            # Sync slider and spinbox
            if self.sender() == self.wl_slider:
                self.wl_spinbox.setValue(value)
            else:
                self.wl_slider.setValue(value)

            # Update ViewModel
            self.tps_viewmodel.set_window_level(self.ww_slider.value(), value)

    def _on_zoom_changed(self, value):
        """Handle zoom changes."""
        if self.tps_viewmodel:
            zoom_factor = value / 100.0  # Convert from percentage
            self.tps_viewmodel.set_zoom_factor(zoom_factor)
            self.zoom_info_label.setText(f"{value}%")

    def _reset_zoom(self):
        """Reset zoom to 100%."""
        self.zoom_slider.setValue(100)
        if self.tps_viewmodel:
            self.tps_viewmodel.set_zoom_factor(1.0)
        self.zoom_info_label.setText("100%")

    def _on_image_loaded(self, image_set):
        """Handle image loaded signal from TPS ViewModel."""
        if hasattr(image_set, 'pixel_data'):
            shape = image_set.pixel_data.shape
            total_slices = shape[2] if len(shape) > 2 else 1

            # Update slice controls
            self.slice_slider.setMaximum(total_slices - 1)
            self.slice_spinbox.setMaximum(total_slices)

            # Update image info
            spacing = getattr(image_set, 'pixel_spacing', [1.0, 1.0, 1.0])
            info_text = f"CT - {shape[0]}×{shape[1]}×{total_slices} - Spacing: {spacing[0]:.2f}×{spacing[1]:.2f}×{spacing[2]:.2f}mm"
            self.image_info_label.setText(info_text)

    def _on_slice_changed(self, slice_index):
        """Handle slice changed signal from TPS ViewModel."""
        # Update UI controls (avoid recursion)
        self.slice_slider.blockSignals(True)
        self.slice_spinbox.blockSignals(True)

        self.slice_slider.setValue(slice_index)
        self.slice_spinbox.setValue(slice_index + 1)  # Convert to 1-based

        # Update slice info
        total_slices = self.slice_slider.maximum() + 1
        self.slice_info_label.setText(f"Slice: {slice_index + 1} / {total_slices}")

        self.slice_slider.blockSignals(False)
        self.slice_spinbox.blockSignals(False)

    def _on_window_level_updated(self, window_width, window_level):
        """Handle window/level updated signal from TPS ViewModel."""
        # Update UI controls (avoid recursion)
        self.ww_slider.blockSignals(True)
        self.ww_spinbox.blockSignals(True)
        self.wl_slider.blockSignals(True)
        self.wl_spinbox.blockSignals(True)

        self.ww_slider.setValue(window_width)
        self.ww_spinbox.setValue(window_width)
        self.wl_slider.setValue(window_level)
        self.wl_spinbox.setValue(window_level)

        self.ww_slider.blockSignals(False)
        self.ww_spinbox.blockSignals(False)
        self.wl_slider.blockSignals(False)
        self.wl_spinbox.blockSignals(False)
    
    def _show_all_rois(self):
        """Show all ROIs."""
        for i in range(self.overlay_viewmodel.rois_model.rowCount()):
            self.overlay_viewmodel.set_roi_visibility(i, True)
    
    def _hide_all_rois(self):
        """Hide all ROIs."""
        for i in range(self.overlay_viewmodel.rois_model.rowCount()):
            self.overlay_viewmodel.set_roi_visibility(i, False)
    
    def _show_all_pois(self):
        """Show all POIs."""
        for i in range(self.overlay_viewmodel.pois_model.rowCount()):
            self.overlay_viewmodel.set_poi_visibility(i, True)
    
    def _hide_all_pois(self):
        """Hide all POIs."""
        for i in range(self.overlay_viewmodel.pois_model.rowCount()):
            self.overlay_viewmodel.set_poi_visibility(i, False)
    
    def _show_all_beams(self):
        """Show all beams."""
        for i in range(self.overlay_viewmodel.beams_model.rowCount()):
            self.overlay_viewmodel.set_beam_visibility(i, True)
    
    def _hide_all_beams(self):
        """Hide all beams."""
        for i in range(self.overlay_viewmodel.beams_model.rowCount()):
            self.overlay_viewmodel.set_beam_visibility(i, False)
    
    def _on_rois_loaded(self):
        """Handle ROIs loaded signal."""
        count = self.overlay_viewmodel.roiCount
        self._update_status()
        logger.debug(f"ROIs loaded: {count}")
    
    def _on_pois_loaded(self):
        """Handle POIs loaded signal."""
        count = self.overlay_viewmodel.poiCount
        self._update_status()
        logger.debug(f"POIs loaded: {count}")
    
    def _on_beams_loaded(self):
        """Handle beams loaded signal."""
        count = self.overlay_viewmodel.beamCount
        self._update_status()
        logger.debug(f"Beams loaded: {count}")
    
    def _on_dose_loaded(self):
        """Handle dose loaded signal."""
        self._update_status()
        logger.debug("Dose data loaded")
    
    def _on_loading_state_changed(self, is_loading: bool):
        """Handle loading state changes."""
        if is_loading:
            self.overlay_status_label.setText("Loading overlays...")
        else:
            self._update_status()
    
    def _on_error_occurred(self, error_message: str):
        """Handle error messages."""
        self.overlay_status_label.setText(f"Error: {error_message}")
        logger.error(f"Overlay error: {error_message}")
    
    def _update_status(self):
        """Update the status display."""
        roi_count = self.overlay_viewmodel.roiCount
        poi_count = self.overlay_viewmodel.poiCount
        beam_count = self.overlay_viewmodel.beamCount
        
        status_parts = []
        if roi_count > 0:
            status_parts.append(f"{roi_count} ROIs")
        if poi_count > 0:
            status_parts.append(f"{poi_count} POIs")
        if beam_count > 0:
            status_parts.append(f"{beam_count} Beams")
        
        if status_parts:
            status_text = "Loaded: " + ", ".join(status_parts)
        else:
            status_text = "No overlays loaded"
        
        self.overlay_status_label.setText(status_text)
    
    def clear_overlays(self):
        """Clear all overlay data."""
        self.overlay_viewmodel.clear_overlays()
        self.overlay_status_label.setText("No overlays loaded")
