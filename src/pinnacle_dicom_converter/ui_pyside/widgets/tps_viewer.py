"""
TPS Viewer widget with VTK integration.

This module provides the main TPS viewer widget with single viewport
and fast orientation switching capabilities.
"""

import logging
from typing import Optional, Callable

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
    QSlider, QSpinBox, QButtonGroup, QFrame, QSizePolicy
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont

# VTK imports
from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
from vtkmodules.vtkRenderingCore import vtkRenderer
from vtkmodules.vtkInteractionStyle import vtkInteractorStyleImage

from ..vtk_pipeline.renderer import TPSRenderer
from pinnacle_dicom_converter.core.models.image_set import ImageSet

logger = logging.getLogger(__name__)


class TPSViewer(QWidget):
    """High-performance TPS viewer widget with VTK integration.
    
    This widget provides:
    - Single viewport with fast orientation switching
    - VTK-based CT rendering with hardware acceleration
    - Medical imaging interaction style
    - Orientation toolbar and slice navigation
    - Window/level controls
    
    Signals:
        orientationChanged: Emitted when orientation changes
        sliceChanged: Emitted when slice index changes
        windowLevelChanged: Emitted when window/level changes
    """
    
    # Qt signals for MVVM integration
    orientationChanged = Signal(str)  # orientation
    sliceChanged = Signal(int)  # slice_index
    windowLevelChanged = Signal(int, int)  # window_width, window_level
    
    def __init__(self, parent=None):
        """Initialize the TPS viewer widget."""
        super().__init__(parent)
        
        # Internal state
        self.tps_renderer: Optional[TPSRenderer] = None
        self.current_orientation = "axial"
        self.current_slice = 0
        self.total_slices = 0
        self.window_width = 1400
        self.window_level = 1000
        
        # UI components
        self.vtk_widget: Optional[QVTKRenderWindowInteractor] = None
        self.orientation_buttons = {}
        self.slice_slider: Optional[QSlider] = None
        self.slice_spinbox: Optional[QSpinBox] = None
        self.info_label: Optional[QLabel] = None
        
        self.setup_ui()
        self.setup_vtk()
        self.setup_connections()
        
        logger.info("TPS viewer widget initialized")
    
    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # Orientation toolbar
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(10, 5, 10, 5)
        
        # Orientation label
        orientation_label = QLabel("Orientation:")
        orientation_label.setFont(QFont("Arial", 10, QFont.Bold))
        toolbar_layout.addWidget(orientation_label)
        
        # Orientation buttons
        self.orientation_group = QButtonGroup(self)
        orientations = [("Axial", "axial"), ("Sagittal", "sagittal"), ("Coronal", "coronal")]
        
        for text, orientation in orientations:
            btn = QPushButton(text)
            btn.setCheckable(True)
            btn.setMinimumWidth(80)
            btn.setProperty("orientation", orientation)
            self.orientation_buttons[orientation] = btn
            self.orientation_group.addButton(btn)
            toolbar_layout.addWidget(btn)
        
        # Set axial as default
        self.orientation_buttons["axial"].setChecked(True)
        
        toolbar_layout.addStretch()
        
        # Slice navigation
        slice_label = QLabel("Slice:")
        slice_label.setFont(QFont("Arial", 10, QFont.Bold))
        toolbar_layout.addWidget(slice_label)
        
        self.slice_slider = QSlider(Qt.Horizontal)
        self.slice_slider.setMinimum(0)
        self.slice_slider.setMaximum(100)
        self.slice_slider.setValue(50)
        self.slice_slider.setMinimumWidth(150)
        toolbar_layout.addWidget(self.slice_slider)
        
        self.slice_spinbox = QSpinBox()
        self.slice_spinbox.setMinimum(0)
        self.slice_spinbox.setMaximum(100)
        self.slice_spinbox.setValue(50)
        self.slice_spinbox.setMinimumWidth(60)
        toolbar_layout.addWidget(self.slice_spinbox)
        
        toolbar_layout.addStretch()
        
        # Info display
        self.info_label = QLabel("No image loaded")
        self.info_label.setStyleSheet("color: #666666; font-style: italic;")
        toolbar_layout.addWidget(self.info_label)
        
        layout.addWidget(toolbar_frame)
        
        # VTK widget
        self.vtk_widget = QVTKRenderWindowInteractor(self)
        self.vtk_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        layout.addWidget(self.vtk_widget)
        
        # Status bar
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(10, 5, 10, 5)
        
        status_layout.addWidget(QLabel("Navigation: ↑↓ (slice), PgUp/PgDn (5 slices), Mouse: zoom/pan, Ctrl+Mouse: window/level"))
        status_layout.addStretch()
        
        layout.addWidget(status_frame)
    
    def setup_vtk(self):
        """Set up the VTK rendering pipeline."""
        if self.vtk_widget is None:
            logger.error("VTK widget not initialized")
            return

        try:
            # Create VTK renderer
            renderer = vtkRenderer()
            renderer.SetBackground(0.0, 0.0, 0.0)  # Black background

            # Add renderer to render window
            self.vtk_widget.GetRenderWindow().AddRenderer(renderer)

            # Set up medical imaging interaction style
            style = vtkInteractorStyleImage()
            self.vtk_widget.GetRenderWindow().GetInteractor().SetInteractorStyle(style)

            # Create TPS renderer
            self.tps_renderer = TPSRenderer(renderer)

            # Enable interaction
            self.vtk_widget.GetRenderWindow().GetInteractor().Initialize()

            logger.info("VTK pipeline configured")

        except Exception as e:
            logger.error(f"Failed to setup VTK: {e}")
            # Create a simple placeholder if VTK fails
            self.tps_renderer = None
    
    def setup_connections(self):
        """Set up signal/slot connections."""
        # Orientation buttons
        self.orientation_group.buttonClicked.connect(self._on_orientation_clicked)
        
        # Slice navigation
        self.slice_slider.valueChanged.connect(self._on_slice_slider_changed)
        self.slice_spinbox.valueChanged.connect(self._on_slice_spinbox_changed)
        
        # Keyboard shortcuts will be handled by the main window
        
    def load_image_set(self, image_set: ImageSet) -> bool:
        """Load CT image data into the viewer.
        
        Args:
            image_set: ImageSet with pixel_data
            
        Returns:
            True if successful, False otherwise
        """
        if self.tps_renderer is None:
            logger.error("TPS renderer not initialized")
            return False
        
        try:
            # Load data into VTK pipeline
            success = self.tps_renderer.set_image_data(image_set)
            if not success:
                return False
            
            # Update UI controls
            self._update_slice_controls()
            self._update_info_display()
            
            # Render
            self.vtk_widget.GetRenderWindow().Render()
            
            logger.info("Image set loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load image set: {e}")
            return False
    
    def set_orientation(self, orientation: str) -> bool:
        """Set the viewing orientation.
        
        Args:
            orientation: "axial", "sagittal", or "coronal"
            
        Returns:
            True if successful, False otherwise
        """
        if orientation not in self.orientation_buttons:
            return False
        
        if self.tps_renderer is None:
            return False
        
        # Update VTK renderer
        success = self.tps_renderer.set_orientation(orientation)
        if not success:
            return False
        
        # Update UI
        self.current_orientation = orientation
        self.orientation_buttons[orientation].setChecked(True)
        self._update_slice_controls()
        self._update_info_display()
        
        # Emit signal
        self.orientationChanged.emit(orientation)
        
        return True
    
    def navigate_slice(self, delta: int) -> bool:
        """Navigate by a relative number of slices.
        
        Args:
            delta: Number of slices to move
            
        Returns:
            True if successful, False otherwise
        """
        if self.tps_renderer is None:
            return False
        
        success = self.tps_renderer.navigate_slice(delta)
        if success:
            self._update_slice_controls()
            self._update_info_display()
            self.sliceChanged.emit(self.tps_renderer.current_slice_index)
        
        return success
    
    def set_window_level(self, window_width: int, window_level: int):
        """Update window/level settings.
        
        Args:
            window_width: Window width
            window_level: Window level
        """
        if self.tps_renderer is None:
            return
        
        self.window_width = window_width
        self.window_level = window_level
        
        self.tps_renderer.set_window_level(window_width, window_level)
        self.windowLevelChanged.emit(window_width, window_level)
    
    def _on_orientation_clicked(self, button):
        """Handle orientation button clicks."""
        orientation = button.property("orientation")
        if orientation:
            self.set_orientation(orientation)
    
    def _on_slice_slider_changed(self, value):
        """Handle slice slider changes."""
        if self.tps_renderer is None:
            return
        
        # Prevent recursive updates
        if self.slice_spinbox.value() != value:
            self.slice_spinbox.setValue(value)
        
        self.tps_renderer.set_slice_index(value)
        self._update_info_display()
        self.sliceChanged.emit(value)
    
    def _on_slice_spinbox_changed(self, value):
        """Handle slice spinbox changes."""
        if self.tps_renderer is None:
            return
        
        # Prevent recursive updates
        if self.slice_slider.value() != value:
            self.slice_slider.setValue(value)
        
        self.tps_renderer.set_slice_index(value)
        self._update_info_display()
        self.sliceChanged.emit(value)
    
    def _update_slice_controls(self):
        """Update slice navigation controls."""
        if self.tps_renderer is None:
            return
        
        slice_info = self.tps_renderer.get_current_slice_info()
        max_slices = slice_info['total_slices']
        current_slice = slice_info['slice_index']
        
        # Update slider and spinbox ranges
        self.slice_slider.setMaximum(max_slices - 1)
        self.slice_spinbox.setMaximum(max_slices - 1)
        
        # Update current values
        self.slice_slider.setValue(current_slice)
        self.slice_spinbox.setValue(current_slice)
        
        self.total_slices = max_slices
        self.current_slice = current_slice
    
    def _update_info_display(self):
        """Update the information display."""
        if self.tps_renderer is None:
            self.info_label.setText("No image loaded")
            return
        
        slice_info = self.tps_renderer.get_current_slice_info()
        
        info_text = (
            f"{slice_info['orientation'].title()} - "
            f"Slice {slice_info['slice_index'] + 1}/{slice_info['total_slices']} - "
            f"W/L: {slice_info['window_width']}/{slice_info['window_level']}"
        )
        
        self.info_label.setText(info_text)
    
    def keyPressEvent(self, event):
        """Handle keyboard events for navigation."""
        if self.tps_renderer is None:
            super().keyPressEvent(event)
            return
        
        key = event.key()
        
        if key == Qt.Key_Up:
            self.navigate_slice(-1)
        elif key == Qt.Key_Down:
            self.navigate_slice(1)
        elif key == Qt.Key_PageUp:
            self.navigate_slice(-5)
        elif key == Qt.Key_PageDown:
            self.navigate_slice(5)
        elif key == Qt.Key_A:
            self.set_orientation("axial")
        elif key == Qt.Key_S:
            self.set_orientation("sagittal")
        elif key == Qt.Key_C:
            self.set_orientation("coronal")
        else:
            super().keyPressEvent(event)
