"""
Navigation dock widget for patient/plan/trial selection.

This widget provides the left dock interface for hierarchical navigation
through Pinnacle data using the NavigationViewModel.
"""

import logging
from typing import Optional

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QListView, 
    QPushButton, QFrame, QSizePolicy
)
from PySide6.QtCore import Qt, QModelIndex

from ..viewmodels.navigation_viewmodel import NavigationViewModel

logger = logging.getLogger(__name__)


class NavigationDock(QWidget):
    """Navigation dock widget for patient/plan/trial selection.
    
    This widget provides a hierarchical interface for navigating through
    Pinnacle data structure with cascading selection.
    """
    
    def __init__(self, navigation_viewmodel: NavigationViewModel, parent=None):
        """Initialize the navigation dock.
        
        Args:
            navigation_viewmodel: NavigationViewModel instance
            parent: Parent widget
        """
        super().__init__(parent)
        
        self.navigation_viewmodel = navigation_viewmodel
        
        # UI components
        self.patients_list: Optional[QListView] = None
        self.plans_list: Optional[QListView] = None
        self.trials_list: Optional[QListView] = None
        
        self.setup_ui()
        self.setup_connections()
        
        logger.info("Navigation dock initialized")
    
    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Patients section
        patients_frame = self._create_section_frame("Patients")
        patients_layout = QVBoxLayout(patients_frame)
        
        self.patients_list = QListView()
        self.patients_list.setModel(self.navigation_viewmodel.patients_model)
        self.patients_list.setMaximumHeight(150)
        self.patients_list.setSelectionMode(QListView.SingleSelection)
        patients_layout.addWidget(self.patients_list)
        
        layout.addWidget(patients_frame)
        
        # Plans section
        plans_frame = self._create_section_frame("Plans")
        plans_layout = QVBoxLayout(plans_frame)
        
        self.plans_list = QListView()
        self.plans_list.setModel(self.navigation_viewmodel.plans_model)
        self.plans_list.setMaximumHeight(120)
        self.plans_list.setSelectionMode(QListView.SingleSelection)
        plans_layout.addWidget(self.plans_list)
        
        layout.addWidget(plans_frame)
        
        # Trials section
        trials_frame = self._create_section_frame("Trials")
        trials_layout = QVBoxLayout(trials_frame)
        
        self.trials_list = QListView()
        self.trials_list.setModel(self.navigation_viewmodel.trials_model)
        self.trials_list.setMaximumHeight(100)
        self.trials_list.setSelectionMode(QListView.SingleSelection)
        trials_layout.addWidget(self.trials_list)
        
        layout.addWidget(trials_frame)
        
        # Add stretch to push everything to top
        layout.addStretch()
        
        # Status section
        status_frame = self._create_section_frame("Status")
        status_layout = QVBoxLayout(status_frame)
        
        self.status_label = QLabel("No dataset loaded")
        self.status_label.setStyleSheet("color: #666666; font-style: italic;")
        self.status_label.setWordWrap(True)
        status_layout.addWidget(self.status_label)
        
        layout.addWidget(status_frame)
    
    def _create_section_frame(self, title: str) -> QFrame:
        """Create a section frame with title.
        
        Args:
            title: Section title
            
        Returns:
            QFrame with title label
        """
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        # Create layout with title
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # Title label
        title_label = QLabel(title)
        title_label.setStyleSheet("font-weight: bold; color: #333333;")
        layout.addWidget(title_label)
        
        return frame
    
    def setup_connections(self):
        """Set up signal/slot connections."""
        # List selection signals
        if self.patients_list:
            self.patients_list.clicked.connect(self._on_patient_clicked)
        
        if self.plans_list:
            self.plans_list.clicked.connect(self._on_plan_clicked)
        
        if self.trials_list:
            self.trials_list.clicked.connect(self._on_trial_clicked)
        
        # ViewModel signals
        self.navigation_viewmodel.patientsLoaded.connect(self._on_patients_loaded)
        self.navigation_viewmodel.plansLoaded.connect(self._on_plans_loaded)
        self.navigation_viewmodel.trialsLoaded.connect(self._on_trials_loaded)
        self.navigation_viewmodel.loadingStateChanged.connect(self._on_loading_state_changed)
        self.navigation_viewmodel.errorOccurred.connect(self._on_error_occurred)
    
    def _on_patient_clicked(self, index: QModelIndex):
        """Handle patient selection."""
        if index.isValid():
            self.navigation_viewmodel.select_patient(index.row())
    
    def _on_plan_clicked(self, index: QModelIndex):
        """Handle plan selection."""
        if index.isValid():
            self.navigation_viewmodel.select_plan(index.row())
    
    def _on_trial_clicked(self, index: QModelIndex):
        """Handle trial selection."""
        if index.isValid():
            self.navigation_viewmodel.select_trial(index.row())
    
    def _on_patients_loaded(self):
        """Handle patients loaded signal."""
        count = self.navigation_viewmodel.patients_model.rowCount()
        self.status_label.setText(f"Loaded {count} patients")
        logger.debug(f"Patients loaded: {count}")
    
    def _on_plans_loaded(self):
        """Handle plans loaded signal."""
        count = self.navigation_viewmodel.plans_model.rowCount()
        logger.debug(f"Plans loaded: {count}")
    
    def _on_trials_loaded(self):
        """Handle trials loaded signal."""
        count = self.navigation_viewmodel.trials_model.rowCount()
        logger.debug(f"Trials loaded: {count}")
    
    def _on_loading_state_changed(self, is_loading: bool):
        """Handle loading state changes."""
        if is_loading:
            self.status_label.setText("Loading...")
        else:
            # Status will be updated by other signals
            pass
    
    def _on_error_occurred(self, error_message: str):
        """Handle error messages."""
        self.status_label.setText(f"Error: {error_message}")
        logger.error(f"Navigation error: {error_message}")
    
    def clear_selection(self):
        """Clear all selections."""
        if self.patients_list:
            self.patients_list.clearSelection()
        if self.plans_list:
            self.plans_list.clearSelection()
        if self.trials_list:
            self.trials_list.clearSelection()
        
        self.status_label.setText("No dataset loaded")
        
        self.navigation_viewmodel.clear_selection()
