"""
ViewModels package for the Pinnacle DICOM Converter GUI.

This package contains Qt-based ViewModels that adapt the existing NiceGUI
ViewModels to work with PySide6 using QObject, signals, and properties.

The ViewModels provide the business logic layer between the UI components
and the data services, implementing the MVVM pattern with Qt's reactive
property system.
"""

from .tps_viewmodel import TPSViewModel
from .navigation_viewmodel import NavigationViewModel
from .overlay_viewmodel import OverlayViewModel

__all__ = ["TPSViewModel", "NavigationViewModel", "OverlayViewModel"]
