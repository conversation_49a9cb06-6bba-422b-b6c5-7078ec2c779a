"""
Navigation ViewModel for patient/plan/trial selection.

This ViewModel adapts the existing NiceGUI NavigationViewModel to work with
PySide6, managing the hierarchical navigation of Pinnacle data.
"""

import logging
from typing import Optional, List, Dict, Any, TYPE_CHECKING

from PySide6.QtCore import QObject, Signal, Property, Slot, QAbstractListModel, QModelIndex, Qt

from pinnacle_dicom_converter.services.pinnacle_api import PinnacleAPI

# Forward declaration to avoid circular imports
if TYPE_CHECKING:
    from .tps_viewmodel import TPSViewModel

logger = logging.getLogger(__name__)


class NavigationListModel(QAbstractListModel):
    """Qt list model for navigation items."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._items: List[Dict[str, Any]] = []
    
    def rowCount(self, parent=QModelIndex()):
        return len(self._items)
    
    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or index.row() >= len(self._items):
            return None
        
        item = self._items[index.row()]
        if role == Qt.DisplayRole:
            return item.get('name', '')
        elif role == Qt.UserRole:
            return item
        
        return None
    
    def setItems(self, items: List[Dict[str, Any]]):
        """Update the model with new items."""
        self.beginResetModel()
        self._items = items
        self.endResetModel()

    def clear(self):
        """Clear all items from the model."""
        self.beginResetModel()
        self._items = []
        self.endResetModel()

    def getItem(self, index: int) -> Optional[Dict[str, Any]]:
        """Get item at index."""
        if 0 <= index < len(self._items):
            return self._items[index]
        return None


class NavigationViewModel(QObject):
    """Qt-based ViewModel for patient/plan/trial navigation.
    
    This ViewModel manages the hierarchical navigation system for Pinnacle data,
    providing models for patient, plan, and trial selection with cascading updates.
    
    Signals:
        patientsLoaded: Emitted when patient list is loaded
        plansLoaded: Emitted when plan list is loaded
        trialsLoaded: Emitted when trial list is loaded
        patientSelected: Emitted when a patient is selected
        planSelected: Emitted when a plan is selected
        trialSelected: Emitted when a trial is selected
        loadingStateChanged: Emitted when loading state changes
        errorOccurred: Emitted when an error occurs
    """
    
    # Qt Signals
    patientsLoaded = Signal()
    plansLoaded = Signal()
    trialsLoaded = Signal()
    patientSelected = Signal(dict)  # patient_info
    planSelected = Signal(dict)  # plan_info
    trialSelected = Signal(dict)  # trial_info
    loadingStateChanged = Signal(bool)  # is_loading
    errorOccurred = Signal(str)  # error_message
    
    def __init__(self, tps_viewmodel: Optional['TPSViewModel'] = None, parent=None):
        """Initialize the Navigation ViewModel.

        Args:
            tps_viewmodel: TPSViewModel instance for image loading
            parent: Parent QObject
        """
        super().__init__(parent)

        # Connected ViewModels
        self.tps_viewmodel = tps_viewmodel
        
        # Data models
        self.patients_model = NavigationListModel(self)
        self.plans_model = NavigationListModel(self)
        self.trials_model = NavigationListModel(self)
        
        # Current selections
        self._selected_patient_index = -1
        self._selected_plan_index = -1
        self._selected_trial_index = -1
        
        # Data storage
        self._patients_data: List[Dict[str, Any]] = []
        self._plans_data: List[Dict[str, Any]] = []
        self._trials_data: List[Dict[str, Any]] = []
        
        # Service reference
        self._pinnacle_api: Optional[PinnacleAPI] = None
        
        # Loading state
        self._is_loading = False
        
        logger.info("Navigation ViewModel initialized")

    def set_pinnacle_api(self, pinnacle_api: Optional[PinnacleAPI]):
        """Set the PinnacleAPI instance for data access.

        Args:
            pinnacle_api: PinnacleAPI instance or None to clear
        """
        self._pinnacle_api = pinnacle_api
        if pinnacle_api:
            # Load patients when API is set
            self.load_patients()
        else:
            # Clear data when API is cleared
            self.clear_all_data()

    # Qt Properties
    @Property(int)
    def selectedPatientIndex(self):
        """Currently selected patient index."""
        return self._selected_patient_index
    
    @Property(int)
    def selectedPlanIndex(self):
        """Currently selected plan index."""
        return self._selected_plan_index
    
    @Property(int)
    def selectedTrialIndex(self):
        """Currently selected trial index."""
        return self._selected_trial_index
    
    @Property(bool, notify=loadingStateChanged)
    def isLoading(self):
        """Whether data is currently loading."""
        return self._is_loading
    
    # Public Methods
    @Slot(object)
    def set_pinnacle_api(self, pinnacle_api: PinnacleAPI):
        """Set the PinnacleAPI instance and load patients.
        
        Args:
            pinnacle_api: PinnacleAPI instance
        """
        self._pinnacle_api = pinnacle_api
        self.load_patients()
    
    @Slot()
    def load_patients(self):
        """Load the list of patients."""
        if self._pinnacle_api is None:
            return
        
        try:
            self._set_loading_state(True)
            
            # Get patient list from API
            institution = self._pinnacle_api.get_institution()
            if not institution:
                raise Exception(f"Failed to get institution: {self._pinnacle_api.source_path}")
            
            if not institution.patient_lite_list:
                raise Exception(f"No patients found in institution: {institution}")
            
            if not institution.institution_path:
                institution.institution_path = self._pinnacle_api.source_path / f"Institution_{institution.institution_id}"
            
            if not institution.default_mount_point:
                institution.default_mount_point = "Mount_0"
            
            # Convert to display format
            self._patients_data = []
            for patient in institution.patient_lite_list:

                patient_info = {
                    'id': patient.patient_id,
                    'name': f"{patient.last_name}, {patient.first_name}",
                    'patient_id': patient.patient_id,
                    'first_name': patient.first_name,
                    'last_name': patient.last_name,
                    'birth_date': getattr(patient, 'birth_date', ''),
                    'patient_dir': institution.institution_path / institution.default_mount_point / f"Patient_{patient.patient_id}",
                }
                self._patients_data.append(patient_info)
            
            # Update model
            logger.info(f"Setting {len(self._patients_data)} patients to model")
            self.patients_model.setItems(self._patients_data)
            logger.info(f"Model now has {self.patients_model.rowCount()} rows")

            # Clear dependent selections
            self._clear_plans()
            self._clear_trials()

            self.patientsLoaded.emit()
            logger.info(f"Loaded {len(self._patients_data)} patients")
            
        except Exception as e:
            error_msg = f"Failed to load patients: {e}"
            logger.error(error_msg)
            self.errorOccurred.emit(error_msg)
        
        finally:
            self._set_loading_state(False)
    
    @Slot(int)
    def select_patient(self, index: int):
        """Select a patient and load their plans.
        
        Args:
            index: Patient index in the model
        """
        if 0 <= index < len(self._patients_data):
            self._selected_patient_index = index
            patient_info = self._patients_data[index]
            
            self.patientSelected.emit(patient_info)
            self.load_plans(patient_info['patient_dir'])
            
            logger.debug(f"Patient selected: {patient_info['name']}")
    
    @Slot(str)
    def load_plans(self, patient_dir: str):
        """Load plans for the selected patient.
        
        Args:
            patient_dir: Patient directory path
        """
        if self._pinnacle_api is None:
            return
        
        try:
            self._set_loading_state(True)
            
            # Get plan list from API
            plans = self._pinnacle_api.get_plans(patient_dir)
            
            # Convert to display format
            self._plans_data = []
            for plan in plans:
                plan_info = {
                    'id': plan.plan_id,
                    'name': plan.plan_name,
                    'plan_id': plan.plan_id,
                    'plan_name': plan.plan_name,
                    'plan_date': getattr(plan, 'plan_date', ''),
                    'plan_dir': plan.plan_dir,
                    'patient_dir': patient_dir
                }
                self._plans_data.append(plan_info)
            
            # Update model
            self.plans_model.setItems(self._plans_data)
            
            # Clear dependent selections
            self._clear_trials()
            
            self.plansLoaded.emit()
            logger.info(f"Loaded {len(self._plans_data)} plans")
            
        except Exception as e:
            error_msg = f"Failed to load plans: {e}"
            logger.error(error_msg)
            self.errorOccurred.emit(error_msg)
        
        finally:
            self._set_loading_state(False)
    
    @Slot(int)
    def select_plan(self, index: int):
        """Select a plan and load its trials.
        
        Args:
            index: Plan index in the model
        """
        if 0 <= index < len(self._plans_data):
            self._selected_plan_index = index
            plan_info = self._plans_data[index]
            
            self.planSelected.emit(plan_info)
            self.load_trials(plan_info['patient_dir'], plan_info['plan_dir'])
            
            logger.debug(f"Plan selected: {plan_info['name']}")
    
    @Slot(str, str)
    def load_trials(self, patient_dir: str, plan_dir: str):
        """Load trials for the selected plan.
        
        Args:
            patient_dir: Patient directory path
            plan_dir: Plan directory path
        """
        if self._pinnacle_api is None:
            return
        
        try:
            self._set_loading_state(True)
            
            # Get trial list from API
            trials = self._pinnacle_api.get_trials(patient_dir, plan_dir)
            
            # Convert to display format
            self._trials_data = []
            for trial in trials:
                trial_info = {
                    'id': trial.trial_id,
                    'name': trial.trial_name,
                    'trial_id': trial.trial_id,
                    'trial_name': trial.trial_name,
                    'trial_date': getattr(trial, 'trial_date', ''),
                    'trial_dir': trial.trial_dir,
                    'plan_dir': plan_dir,
                    'patient_dir': patient_dir
                }
                self._trials_data.append(trial_info)
            
            # Update model
            self.trials_model.setItems(self._trials_data)
            
            self.trialsLoaded.emit()
            logger.info(f"Loaded {len(self._trials_data)} trials")
            
        except Exception as e:
            error_msg = f"Failed to load trials: {e}"
            logger.error(error_msg)
            self.errorOccurred.emit(error_msg)
        
        finally:
            self._set_loading_state(False)
    
    @Slot(int)
    def select_trial(self, index: int):
        """Select a trial.

        Args:
            index: Trial index in the model
        """
        if 0 <= index < len(self._trials_data):
            self._selected_trial_index = index
            trial_info = self._trials_data[index]

            # Trigger image loading in TPS ViewModel if connected
            if self.tps_viewmodel and self.selected_patient_info:
                patient_id = self.selected_patient_info.get('id')
                if patient_id is not None:
                    self.tps_viewmodel.load_patient_image(patient_id)

            self.trialSelected.emit(trial_info)
            logger.debug(f"Trial selected: {trial_info['name']}")
    
    @Slot()
    def clear_selection(self):
        """Clear all selections."""
        self._selected_patient_index = -1
        self._selected_plan_index = -1
        self._selected_trial_index = -1
        
        self._clear_patients()
        self._clear_plans()
        self._clear_trials()
    
    # Helper Methods
    def _set_loading_state(self, is_loading: bool):
        """Update loading state and emit signal if changed."""
        if is_loading != self._is_loading:
            self._is_loading = is_loading
            self.loadingStateChanged.emit(is_loading)
    
    def _clear_patients(self):
        """Clear patients data."""
        self._patients_data.clear()
        self.patients_model.setItems([])
    
    def _clear_plans(self):
        """Clear plans data."""
        self._plans_data.clear()
        self.plans_model.setItems([])
        self._selected_plan_index = -1
    
    def _clear_trials(self):
        """Clear trials data."""
        self._trials_data.clear()
        self.trials_model.setItems([])
        self._selected_trial_index = -1
    
    # Public Properties for external access
    @property
    def selected_patient_info(self) -> Optional[Dict[str, Any]]:
        """Get selected patient info."""
        if 0 <= self._selected_patient_index < len(self._patients_data):
            return self._patients_data[self._selected_patient_index]
        return None
    
    @property
    def selected_plan_info(self) -> Optional[Dict[str, Any]]:
        """Get selected plan info."""
        if 0 <= self._selected_plan_index < len(self._plans_data):
            return self._plans_data[self._selected_plan_index]
        return None
    
    @property
    def selected_trial_info(self) -> Optional[Dict[str, Any]]:
        """Get selected trial info."""
        if 0 <= self._selected_trial_index < len(self._trials_data):
            return self._trials_data[self._selected_trial_index]
        return None

    def clear_all_data(self):
        """Clear all data and selections."""
        self._selected_patient_index = -1
        self._selected_plan_index = -1
        self._selected_trial_index = -1

        self._patients_data.clear()
        self._plans_data.clear()
        self._trials_data.clear()

        self.patients_model.clear()
        self.plans_model.clear()
        self.trials_model.clear()

        self.patientsLoaded.emit()
        self.plansLoaded.emit()
        self.trialsLoaded.emit()

        logger.debug("All data cleared")
