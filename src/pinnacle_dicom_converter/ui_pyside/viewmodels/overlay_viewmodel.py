"""
Overlay ViewModel for managing ROI/POI/Beam/Dose overlays.

This ViewModel adapts the existing NiceGUI OverlayViewModel to work with
PySide6 and VTK, managing the display state of all TPS overlays.
"""

import logging
from typing import Optional, List, Dict, Any, Tuple

from PySide6.QtCore import QObject, Signal, Property, Slot, QAbstractListModel, QModelIndex, Qt

from pinnacle_dicom_converter.services.pinnacle_api import PinnacleAPI

logger = logging.getLogger(__name__)


class OverlayListModel(QAbstractListModel):
    """Qt list model for overlay items with visibility and color."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._items: List[Dict[str, Any]] = []
    
    def rowCount(self, parent=QModelIndex()):
        return len(self._items)
    
    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or index.row() >= len(self._items):
            return None
        
        item = self._items[index.row()]
        if role == Qt.DisplayRole:
            return item.get('name', '')
        elif role == Qt.CheckStateRole:
            return Qt.Checked if item.get('visible', False) else Qt.Unchecked
        elif role == Qt.UserRole:
            return item
        
        return None
    
    def setData(self, index, value, role=Qt.EditRole):
        if not index.isValid() or index.row() >= len(self._items):
            return False
        
        if role == Qt.CheckStateRole:
            self._items[index.row()]['visible'] = (value == Qt.Checked)
            self.dataChanged.emit(index, index, [role])
            return True
        
        return False
    
    def flags(self, index):
        if not index.isValid():
            return Qt.NoItemFlags
        return Qt.ItemIsEnabled | Qt.ItemIsSelectable | Qt.ItemIsUserCheckable
    
    def setItems(self, items: List[Dict[str, Any]]):
        """Update the model with new items."""
        self.beginResetModel()
        self._items = items
        self.endResetModel()
    
    def getItem(self, index: int) -> Optional[Dict[str, Any]]:
        """Get item at index."""
        if 0 <= index < len(self._items):
            return self._items[index]
        return None
    
    def updateItemVisibility(self, index: int, visible: bool):
        """Update visibility of item at index."""
        if 0 <= index < len(self._items):
            self._items[index]['visible'] = visible
            model_index = self.createIndex(index, 0)
            self.dataChanged.emit(model_index, model_index, [Qt.CheckStateRole])


class OverlayViewModel(QObject):
    """Qt-based ViewModel for managing ROI/POI/Beam/Dose overlays.
    
    This ViewModel maintains the state of all overlays and provides methods
    to manipulate their visibility, colors, and other display properties.
    
    Signals:
        roisLoaded: Emitted when ROI list is loaded
        poisLoaded: Emitted when POI list is loaded
        beamsLoaded: Emitted when beam list is loaded
        doseLoaded: Emitted when dose data is loaded
        overlayVisibilityChanged: Emitted when overlay visibility changes
        overlayColorChanged: Emitted when overlay color changes
        loadingStateChanged: Emitted when loading state changes
        errorOccurred: Emitted when an error occurs
    """
    
    # Qt Signals
    roisLoaded = Signal()
    poisLoaded = Signal()
    beamsLoaded = Signal()
    doseLoaded = Signal()
    overlayVisibilityChanged = Signal(str, int, bool)  # overlay_type, item_id, visible
    overlayColorChanged = Signal(str, int, str)  # overlay_type, item_id, color
    loadingStateChanged = Signal(bool)  # is_loading
    errorOccurred = Signal(str)  # error_message
    
    def __init__(self, parent=None):
        """Initialize the Overlay ViewModel.
        
        Args:
            parent: Parent QObject
        """
        super().__init__(parent)
        
        # Data models
        self.rois_model = OverlayListModel(self)
        self.pois_model = OverlayListModel(self)
        self.beams_model = OverlayListModel(self)
        
        # Data storage
        self._rois_data: List[Dict[str, Any]] = []
        self._pois_data: List[Dict[str, Any]] = []
        self._beams_data: List[Dict[str, Any]] = []
        self._dose_data: Optional[Dict[str, Any]] = None
        
        # Service reference
        self._pinnacle_api: Optional[PinnacleAPI] = None
        
        # Loading state
        self._is_loading = False
        
        # Default colors
        self._default_roi_colors = [
            "#FF0000", "#00FF00", "#0000FF", "#FFFF00",
            "#FF00FF", "#00FFFF", "#FF8000", "#8000FF"
        ]
        
        # Connect model signals
        self.rois_model.dataChanged.connect(self._on_roi_data_changed)
        self.pois_model.dataChanged.connect(self._on_poi_data_changed)
        self.beams_model.dataChanged.connect(self._on_beam_data_changed)
        
        logger.info("Overlay ViewModel initialized")
    
    # Qt Properties
    @Property(bool, notify=loadingStateChanged)
    def isLoading(self):
        """Whether data is currently loading."""
        return self._is_loading
    
    @Property(int)
    def roiCount(self):
        """Number of ROIs."""
        return len(self._rois_data)
    
    @Property(int)
    def poiCount(self):
        """Number of POIs."""
        return len(self._pois_data)
    
    @Property(int)
    def beamCount(self):
        """Number of beams."""
        return len(self._beams_data)
    
    # Public Methods
    @Slot(object, str, str, str)
    def load_overlays(self, pinnacle_api: PinnacleAPI, patient_dir: str, plan_dir: str, trial_dir: str):
        """Load all overlay data for the selected trial.
        
        Args:
            pinnacle_api: PinnacleAPI instance
            patient_dir: Patient directory
            plan_dir: Plan directory
            trial_dir: Trial directory
        """
        self._pinnacle_api = pinnacle_api
        
        try:
            self._set_loading_state(True)
            
            # Load ROIs
            self._load_rois(patient_dir, plan_dir)
            
            # Load POIs
            self._load_pois(patient_dir, plan_dir)
            
            # Load Beams
            self._load_beams(patient_dir, plan_dir, trial_dir)
            
            # Load Dose (if available)
            self._load_dose(patient_dir, plan_dir, trial_dir)
            
            logger.info("All overlays loaded successfully")
            
        except Exception as e:
            error_msg = f"Failed to load overlays: {e}"
            logger.error(error_msg)
            self.errorOccurred.emit(error_msg)
        
        finally:
            self._set_loading_state(False)
    
    @Slot(int, bool)
    def set_roi_visibility(self, roi_index: int, visible: bool):
        """Set ROI visibility.
        
        Args:
            roi_index: ROI index in the model
            visible: Visibility state
        """
        if 0 <= roi_index < len(self._rois_data):
            roi_data = self._rois_data[roi_index]
            roi_data['visible'] = visible
            self.rois_model.updateItemVisibility(roi_index, visible)
            self.overlayVisibilityChanged.emit("roi", roi_data['id'], visible)
    
    @Slot(int, bool)
    def set_poi_visibility(self, poi_index: int, visible: bool):
        """Set POI visibility.
        
        Args:
            poi_index: POI index in the model
            visible: Visibility state
        """
        if 0 <= poi_index < len(self._pois_data):
            poi_data = self._pois_data[poi_index]
            poi_data['visible'] = visible
            self.pois_model.updateItemVisibility(poi_index, visible)
            self.overlayVisibilityChanged.emit("poi", poi_data['id'], visible)
    
    @Slot(int, bool)
    def set_beam_visibility(self, beam_index: int, visible: bool):
        """Set beam visibility.
        
        Args:
            beam_index: Beam index in the model
            visible: Visibility state
        """
        if 0 <= beam_index < len(self._beams_data):
            beam_data = self._beams_data[beam_index]
            beam_data['visible'] = visible
            self.beams_model.updateItemVisibility(beam_index, visible)
            self.overlayVisibilityChanged.emit("beam", beam_data['id'], visible)
    
    @Slot(int, str)
    def set_roi_color(self, roi_index: int, color: str):
        """Set ROI color.
        
        Args:
            roi_index: ROI index in the model
            color: Color string (hex format)
        """
        if 0 <= roi_index < len(self._rois_data):
            roi_data = self._rois_data[roi_index]
            roi_data['color'] = color
            self.overlayColorChanged.emit("roi", roi_data['id'], color)
    
    @Slot()
    def clear_overlays(self):
        """Clear all overlay data."""
        self._rois_data.clear()
        self._pois_data.clear()
        self._beams_data.clear()
        self._dose_data = None
        
        self.rois_model.setItems([])
        self.pois_model.setItems([])
        self.beams_model.setItems([])
        
        logger.info("All overlays cleared")
    
    # Private Methods
    def _load_rois(self, patient_dir: str, plan_dir: str):
        """Load ROI data."""
        if self._pinnacle_api is None:
            return
        
        try:
            rois = self._pinnacle_api.get_rois(patient_dir, plan_dir)
            
            self._rois_data = []
            for i, roi in enumerate(rois):
                color = self._default_roi_colors[i % len(self._default_roi_colors)]
                roi_info = {
                    'id': roi.roi_id,
                    'name': roi.roi_name,
                    'visible': True,
                    'color': color,
                    'roi_object': roi
                }
                self._rois_data.append(roi_info)
            
            self.rois_model.setItems(self._rois_data)
            self.roisLoaded.emit()
            
            logger.info(f"Loaded {len(self._rois_data)} ROIs")
            
        except Exception as e:
            logger.warning(f"Failed to load ROIs: {e}")
    
    def _load_pois(self, patient_dir: str, plan_dir: str):
        """Load POI data."""
        if self._pinnacle_api is None:
            return
        
        try:
            pois = self._pinnacle_api.get_pois(patient_dir, plan_dir)
            
            self._pois_data = []
            for poi in pois:
                poi_info = {
                    'id': poi.poi_id,
                    'name': poi.poi_name,
                    'visible': True,
                    'color': "#FFFF00",  # Yellow default
                    'poi_object': poi
                }
                self._pois_data.append(poi_info)
            
            self.pois_model.setItems(self._pois_data)
            self.poisLoaded.emit()
            
            logger.info(f"Loaded {len(self._pois_data)} POIs")
            
        except Exception as e:
            logger.warning(f"Failed to load POIs: {e}")
    
    def _load_beams(self, patient_dir: str, plan_dir: str, trial_dir: str):
        """Load beam data."""
        if self._pinnacle_api is None:
            return
        
        try:
            beams = self._pinnacle_api.get_beams(patient_dir, plan_dir, trial_dir)
            
            self._beams_data = []
            for beam in beams:
                beam_info = {
                    'id': beam.beam_id,
                    'name': beam.beam_name,
                    'visible': True,
                    'color': "#00FFFF",  # Cyan default
                    'beam_object': beam
                }
                self._beams_data.append(beam_info)
            
            self.beams_model.setItems(self._beams_data)
            self.beamsLoaded.emit()
            
            logger.info(f"Loaded {len(self._beams_data)} beams")
            
        except Exception as e:
            logger.warning(f"Failed to load beams: {e}")
    
    def _load_dose(self, patient_dir: str, plan_dir: str, trial_dir: str):
        """Load dose data."""
        if self._pinnacle_api is None:
            return
        
        try:
            dose = self._pinnacle_api.get_dose(patient_dir, plan_dir, trial_dir)
            
            if dose is not None:
                self._dose_data = {
                    'dose_object': dose,
                    'visible': True,
                    'isodose_levels': [95, 90, 80, 70, 50, 30, 10]  # Default levels
                }
                
                self.doseLoaded.emit()
                logger.info("Dose data loaded")
            
        except Exception as e:
            logger.warning(f"Failed to load dose: {e}")
    
    def _set_loading_state(self, is_loading: bool):
        """Update loading state and emit signal if changed."""
        if is_loading != self._is_loading:
            self._is_loading = is_loading
            self.loadingStateChanged.emit(is_loading)
    
    # Signal handlers
    def _on_roi_data_changed(self, top_left, bottom_right, roles):
        """Handle ROI model data changes."""
        if Qt.CheckStateRole in roles:
            for row in range(top_left.row(), bottom_right.row() + 1):
                roi_data = self._rois_data[row]
                self.overlayVisibilityChanged.emit("roi", roi_data['id'], roi_data['visible'])
    
    def _on_poi_data_changed(self, top_left, bottom_right, roles):
        """Handle POI model data changes."""
        if Qt.CheckStateRole in roles:
            for row in range(top_left.row(), bottom_right.row() + 1):
                poi_data = self._pois_data[row]
                self.overlayVisibilityChanged.emit("poi", poi_data['id'], poi_data['visible'])
    
    def _on_beam_data_changed(self, top_left, bottom_right, roles):
        """Handle beam model data changes."""
        if Qt.CheckStateRole in roles:
            for row in range(top_left.row(), bottom_right.row() + 1):
                beam_data = self._beams_data[row]
                self.overlayVisibilityChanged.emit("beam", beam_data['id'], beam_data['visible'])
    
    # Public Properties for external access
    @property
    def rois_data(self) -> List[Dict[str, Any]]:
        """Get ROIs data."""
        return self._rois_data
    
    @property
    def pois_data(self) -> List[Dict[str, Any]]:
        """Get POIs data."""
        return self._pois_data
    
    @property
    def beams_data(self) -> List[Dict[str, Any]]:
        """Get beams data."""
        return self._beams_data
    
    @property
    def dose_data(self) -> Optional[Dict[str, Any]]:
        """Get dose data."""
        return self._dose_data
