"""
TPS ViewModel for CT image display and navigation.

This ViewModel adapts the existing NiceGUI ImageViewModel to work with
PySide6 and VTK, managing CT image display state and providing the
interface between UI components and data services.
"""

import logging
from typing import Optional
from pathlib import Path

from PySide6.QtCore import QObject, Signal, Property, Slot
from PySide6.QtWidgets import QApplication

from pinnacle_dicom_converter.core.models.image_set import ImageSet
from pinnacle_dicom_converter.core.models.institution import Institution
from pinnacle_dicom_converter.core.models.patient import Patient
from pinnacle_dicom_converter.services.pinnacle_api import PinnacleAPI

logger = logging.getLogger(__name__)


class TPSViewModel(QObject):
    """Qt-based ViewModel for TPS image display.
    
    This ViewModel manages all aspects of CT image display including:
    - Image data loading and management
    - Display parameters (orientation, window/level, zoom)
    - Slice navigation
    - Integration with VTK rendering pipeline
    
    Signals:
        imageLoaded: Emitted when new image data is loaded
        orientationChanged: Emitted when orientation changes
        sliceChanged: Emitted when slice index changes
        windowLevelChanged: Emitted when window/level changes
        loadingStateChanged: Emitted when loading state changes
        errorOccurred: Emitted when an error occurs
    """
    
    # Qt Signals
    imageLoaded = Signal(ImageSet)  # image_set
    orientationChanged = Signal(str)  # orientation
    sliceChanged = Signal(int)  # slice_index
    windowLevelChanged = Signal(int, int)  # window_width, window_level
    loadingStateChanged = Signal(bool)  # is_loading
    errorOccurred = Signal(str)  # error_message
    
    def __init__(self, parent=None):
        """Initialize the TPS ViewModel.
        
        Args:
            parent: Parent QObject
        """
        super().__init__(parent)
        
        # Core data
        self._current_image_set: Optional[ImageSet] = None
        self._pinnacle_api: Optional[PinnacleAPI] = None
        self._current_institution: Optional[Institution] = None
        self._current_patient: Optional[Patient] = None
        
        # Display state
        self._current_orientation = "axial"
        self._current_slice_index = 0
        self._total_slices = 0
        self._window_width = 1400
        self._window_level = 1000
        self._zoom_factor = 1.0
        
        # Loading state
        self._is_loading = False
        self._current_dataset_path: Optional[str] = None
        
        logger.info("TPS ViewModel initialized")
    
    # Qt Properties
    @Property(str, notify=orientationChanged)
    def currentOrientation(self):
        """Current viewing orientation."""
        return self._current_orientation
    
    @Property(int, notify=sliceChanged)
    def currentSliceIndex(self):
        """Current slice index."""
        return self._current_slice_index
    
    @Property(int, notify=sliceChanged)
    def totalSlices(self):
        """Total number of slices."""
        return self._total_slices
    
    @Property(int, notify=windowLevelChanged)
    def windowWidth(self):
        """Window width for CT display."""
        return self._window_width
    
    @Property(int, notify=windowLevelChanged)
    def windowLevel(self):
        """Window level for CT display."""
        return self._window_level
    
    @Property(float)
    def zoomFactor(self):
        """Current zoom factor."""
        return self._zoom_factor
    
    @Property(bool, notify=loadingStateChanged)
    def isLoading(self):
        """Whether data is currently loading."""
        return self._is_loading
    
    @Property(str)
    def currentDatasetPath(self):
        """Path to current dataset."""
        return self._current_dataset_path or ""
    
    # Public Methods
    @Slot(str)
    def load_dataset(self, dataset_path: str) -> bool:
        """Load a Pinnacle dataset from directory or archive.

        Args:
            dataset_path: Path to dataset directory or archive file

        Returns:
            True if loading started successfully, False otherwise
        """
        try:
            self._set_loading_state(True)

            # Create PinnacleAPI instance
            self._pinnacle_api = PinnacleAPI(dataset_path)
            self._current_dataset_path = dataset_path

            # Load institution data
            self._current_institution = self._pinnacle_api.get_institution()
            if not self._current_institution:
                self.errorOccurred.emit("No institution data found in dataset")
                return False

            logger.info(f"Dataset loaded: {dataset_path}")
            return True

        except Exception as e:
            error_msg = f"Failed to load dataset: {e}"
            logger.error(error_msg)
            self.errorOccurred.emit(error_msg)
            return False

        finally:
            self._set_loading_state(False)

    @Slot(int, int)
    def load_patient_image(self, patient_id: int, image_set_id: int = None) -> bool:
        """Load CT image data for a specific patient.

        Args:
            patient_id: Patient ID to load
            image_set_id: Specific image set ID, or None to auto-select planning CT

        Returns:
            True if loading successful, False otherwise
        """
        try:
            self._set_loading_state(True)

            if not self._pinnacle_api or not self._current_institution:
                self.errorOccurred.emit("No dataset loaded")
                return False

            # Load patient data
            self._current_patient = self._pinnacle_api.get_patient(self._current_institution, patient_id)
            if not self._current_patient:
                self.errorOccurred.emit(f"Patient {patient_id} not found")
                return False

            # Determine image set ID if not provided
            if image_set_id is None:
                image_set_id = self._find_planning_image_set_id()
                if image_set_id is None:
                    self.errorOccurred.emit("No planning CT image set found")
                    return False

            # Load CT image data with pixel data
            image_set = self._pinnacle_api.get_image(self._current_institution, self._current_patient, image_set_id)
            if image_set is None:
                self.errorOccurred.emit("Failed to load CT image data")
                return False

            # Update state
            self._current_image_set = image_set

            # Calculate dimensions based on pixel data
            if hasattr(image_set, 'pixel_data') and image_set.pixel_data is not None:
                # Pixel data shape is typically (z, y, x) for axial slices
                self._total_slices = image_set.pixel_data.shape[0]
                self._current_slice_index = self._total_slices // 2  # Start at middle slice
            else:
                # Fallback to header dimensions
                self._total_slices = getattr(image_set, 'z_dim', 0)
                self._current_slice_index = self._total_slices // 2

            # Reset display parameters
            self._current_orientation = "axial"
            self._window_width = 1400
            self._window_level = 1000
            self._zoom_factor = 1.0

            # Emit signals
            self.imageLoaded.emit(image_set)
            self.sliceChanged.emit(self._current_slice_index)
            self.orientationChanged.emit(self._current_orientation)
            self.windowLevelChanged.emit(self._window_width, self._window_level)

            logger.info(f"Patient {patient_id} image loaded (image_set_id: {image_set_id})")
            return True

        except Exception as e:
            error_msg = f"Failed to load patient image: {e}"
            logger.error(error_msg)
            self.errorOccurred.emit(error_msg)
            return False

        finally:
            self._set_loading_state(False)
    
    @Slot(str)
    def set_orientation(self, orientation: str):
        """Set the viewing orientation.
        
        Args:
            orientation: "axial", "sagittal", or "coronal"
        """
        if orientation in ("axial", "sagittal", "coronal") and orientation != self._current_orientation:
            self._current_orientation = orientation
            
            # Reset slice to middle when changing orientation
            if self._current_image_set and hasattr(self._current_image_set, 'pixel_data'):
                if orientation == "axial":
                    max_slices = self._current_image_set.pixel_data.shape[0]
                elif orientation == "sagittal":
                    max_slices = self._current_image_set.pixel_data.shape[2]
                else:  # coronal
                    max_slices = self._current_image_set.pixel_data.shape[1]
                
                self._current_slice_index = max_slices // 2
                self._total_slices = max_slices
                self.sliceChanged.emit(self._current_slice_index)
            
            self.orientationChanged.emit(orientation)
            logger.debug(f"Orientation changed to: {orientation}")
    
    @Slot(int)
    def set_slice_index(self, slice_index: int):
        """Set the current slice index.
        
        Args:
            slice_index: Target slice index (0-based)
        """
        if self._current_image_set is None:
            return
        
        # Clamp slice index to valid range
        max_slices = self._get_max_slices_for_orientation()
        slice_index = max(0, min(slice_index, max_slices - 1))
        
        if slice_index != self._current_slice_index:
            self._current_slice_index = slice_index
            self.sliceChanged.emit(slice_index)
    
    @Slot(int)
    def navigate_slice(self, delta: int):
        """Navigate by a relative number of slices.
        
        Args:
            delta: Number of slices to move (positive or negative)
        """
        new_index = self._current_slice_index + delta
        self.set_slice_index(new_index)
    
    @Slot(int, int)
    def set_window_level(self, window_width: int, window_level: int):
        """Set window/level for CT display.
        
        Args:
            window_width: Window width (contrast range)
            window_level: Window level (center value)
        """
        if window_width != self._window_width or window_level != self._window_level:
            self._window_width = window_width
            self._window_level = window_level
            self.windowLevelChanged.emit(window_width, window_level)
    
    @Slot(float)
    def set_zoom_factor(self, zoom_factor: float):
        """Set zoom factor.
        
        Args:
            zoom_factor: Zoom level (1.0 = 100%)
        """
        zoom_factor = max(0.1, min(zoom_factor, 10.0))  # Clamp to reasonable range
        if zoom_factor != self._zoom_factor:
            self._zoom_factor = zoom_factor
    
    @Slot()
    def close_dataset(self):
        """Close the current dataset."""
        self._current_image_set = None
        self._pinnacle_api = None
        self._current_institution = None
        self._current_patient = None
        self._current_dataset_path = None
        self._total_slices = 0
        self._current_slice_index = 0

        logger.info("Dataset closed")
    
    # Helper Methods
    def _set_loading_state(self, is_loading: bool):
        """Update loading state and emit signal if changed."""
        if is_loading != self._is_loading:
            self._is_loading = is_loading
            self.loadingStateChanged.emit(is_loading)
    
    def _get_max_slices_for_orientation(self) -> int:
        """Get maximum slices for current orientation."""
        if self._current_image_set is None or not hasattr(self._current_image_set, 'pixel_data'):
            return 0

        if self._current_orientation == "axial":
            return self._current_image_set.pixel_data.shape[0]
        elif self._current_orientation == "sagittal":
            return self._current_image_set.pixel_data.shape[2]
        else:  # coronal
            return self._current_image_set.pixel_data.shape[1]

    def _find_planning_image_set_id(self) -> Optional[int]:
        """Find the primary planning CT image set ID.

        Returns:
            Image set ID of the planning CT, or None if not found
        """
        if not self._current_patient or not hasattr(self._current_patient, 'image_set_lite_list'):
            return None

        # Look for CT modality image sets
        ct_image_sets = [
            img_set for img_set in self._current_patient.image_set_lite_list
            if getattr(img_set, 'modality', '').upper() == 'CT'
        ]

        if not ct_image_sets:
            # Fallback: use first image set if no CT found
            if self._current_patient.image_set_lite_list:
                return self._current_patient.image_set_lite_list[0].image_set_id
            return None

        # Prefer image sets with "planning" or similar in the name
        planning_keywords = ['planning', 'plan', 'primary', 'treatment']
        for img_set in ct_image_sets:
            name = getattr(img_set, 'image_name', '').lower()
            series_desc = getattr(img_set, 'series_description', '').lower()
            if any(keyword in name or keyword in series_desc for keyword in planning_keywords):
                return img_set.image_set_id

        # Fallback: use first CT image set
        return ct_image_sets[0].image_set_id
    
    # Public Properties for external access
    @property
    def current_image_set(self) -> Optional[ImageSet]:
        """Get current image set."""
        return self._current_image_set
    
    @property
    def pinnacle_api(self) -> Optional[PinnacleAPI]:
        """Get current PinnacleAPI instance."""
        return self._pinnacle_api
