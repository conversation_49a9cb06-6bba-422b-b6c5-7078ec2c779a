"""
Main window implementation for the PySide6/VTK GUI.

This module provides the main application window with dock widgets for
navigation and controls, and a central area for the TPS viewer.
"""

import logging
from pathlib import Path
from PySide6.QtWidgets import (
    QMainWindow, QDockWidget, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QSizePolicy, QStatusBar
)
from PySide6.QtCore import Qt, QSize, QTimer
from PySide6.QtGui import QAction

from .widgets.menu_bar import create_menu_bar
from .widgets.tps_viewer import TPSViewer
from .widgets.navigation_dock import NavigationDock
from .widgets.overlay_dock import OverlayDock
from .viewmodels.tps_viewmodel import TPSViewModel
from .viewmodels.navigation_viewmodel import NavigationViewModel
from .viewmodels.overlay_viewmodel import OverlayViewModel

logger = logging.getLogger(__name__)


class MainWindow(QMainWindow):
    """Main application window with dock-based layout.
    
    Provides the main container for the TPS viewer application with:
    - Custom menu bar
    - Left dock widget for patient/plan/trial navigation
    - Right dock widget for TPS controls
    - Central widget for TPS viewer
    - Status bar for application feedback
    """
    
    def __init__(self):
        """Initialize the main window."""
        super().__init__()

        # Set window properties
        self.setWindowTitle("Pinnacle DICOM Converter")
        self.setMinimumSize(1400, 900)
        self.resize(1800, 1000)

        # Initialize ViewModels
        self.setup_viewmodels()

        # Initialize UI components
        self.setup_ui()
        self.setup_docks()
        self.setup_menu_bar()
        self.setup_status_bar()

        # Connect ViewModels to UI
        self.setup_connections()

        # Auto-load test data after window is shown
        QTimer.singleShot(100, self._auto_load_test_data)

        logger.info("Main window initialized")
    
    def setup_viewmodels(self):
        """Initialize the ViewModels."""
        self.tps_viewmodel = TPSViewModel(self)
        self.navigation_viewmodel = NavigationViewModel(self.tps_viewmodel, self)
        self.overlay_viewmodel = OverlayViewModel(self)

        logger.info("ViewModels initialized")

    def setup_ui(self):
        """Set up the central widget area."""
        # Create TPS viewer as central widget
        self.tps_viewer = TPSViewer(self)
        self.setCentralWidget(self.tps_viewer)
    
    def setup_docks(self):
        """Set up the left and right dock widgets."""
        # Left dock for navigation
        self.left_dock = QDockWidget("Navigation", self)
        self.left_dock.setObjectName("leftDock")
        self.left_dock.setFixedWidth(400)
        self.left_dock.setFeatures(
            QDockWidget.DockWidgetMovable | QDockWidget.DockWidgetFloatable
        )

        # Create navigation dock widget
        self.navigation_dock = NavigationDock(self.navigation_viewmodel, self)
        self.left_dock.setWidget(self.navigation_dock)
        self.addDockWidget(Qt.LeftDockWidgetArea, self.left_dock)

        # Right dock for overlays
        self.right_dock = QDockWidget("Overlays", self)
        self.right_dock.setObjectName("rightDock")
        self.right_dock.setFixedWidth(400)
        self.right_dock.setFeatures(
            QDockWidget.DockWidgetMovable | QDockWidget.DockWidgetFloatable
        )

        # Create overlay dock widget
        self.overlay_dock = OverlayDock(self.overlay_viewmodel, self.tps_viewmodel, self)
        self.right_dock.setWidget(self.overlay_dock)
        self.addDockWidget(Qt.RightDockWidgetArea, self.right_dock)

        logger.info("Dock widgets configured")
    
    def setup_menu_bar(self):
        """Set up the custom menu bar."""
        menu_bar = create_menu_bar(self)
        self.setMenuBar(menu_bar)
        logger.info("Menu bar configured")
    
    def setup_status_bar(self):
        """Set up the status bar."""
        status_bar = QStatusBar()
        status_bar.showMessage("Ready - Open a Pinnacle dataset to begin")
        self.setStatusBar(status_bar)
        logger.info("Status bar configured")

    def setup_connections(self):
        """Set up connections between ViewModels and UI components."""
        # Connect TPS ViewModel to TPS Viewer
        self.tps_viewmodel.imageLoaded.connect(self.tps_viewer.load_image_set)
        self.tps_viewmodel.orientationChanged.connect(self.tps_viewer.set_orientation)
        self.tps_viewmodel.sliceChanged.connect(self._update_slice_display)
        self.tps_viewmodel.windowLevelChanged.connect(self.tps_viewer.set_window_level)
        self.tps_viewmodel.errorOccurred.connect(self._show_error_message)

        # Connect TPS Viewer to TPS ViewModel
        self.tps_viewer.orientationChanged.connect(self.tps_viewmodel.set_orientation)
        self.tps_viewer.sliceChanged.connect(self.tps_viewmodel.set_slice_index)
        self.tps_viewer.windowLevelChanged.connect(self.tps_viewmodel.set_window_level)

        # Connect menu actions to ViewModels
        if hasattr(self, 'menu_actions'):
            self.menu_actions.open_directory_requested.connect(self._open_directory)
            self.menu_actions.open_archive_requested.connect(self._open_archive)
            self.menu_actions.close_dataset_requested.connect(self.tps_viewmodel.close_dataset)

        # Connect navigation ViewModel
        self.navigation_viewmodel.patientSelected.connect(self._on_patient_selected)
        self.navigation_viewmodel.planSelected.connect(self._on_plan_selected)
        self.navigation_viewmodel.trialSelected.connect(self._on_trial_selected)

        # Connect overlay ViewModel
        self.overlay_viewmodel.overlayVisibilityChanged.connect(self._on_overlay_visibility_changed)

        logger.info("ViewModel connections established")

    # Slot methods for ViewModel integration
    def _open_directory(self):
        """Handle open directory request."""
        from PySide6.QtWidgets import QFileDialog

        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Pinnacle Dataset Directory",
            "",
            QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )

        if directory:
            self.tps_viewmodel.load_dataset(directory)
            self.navigation_viewmodel.set_pinnacle_api(self.tps_viewmodel.pinnacle_api)

    def _open_archive(self):
        """Handle open archive request."""
        from PySide6.QtWidgets import QFileDialog

        archive_file, _ = QFileDialog.getOpenFileName(
            self,
            "Select Pinnacle Dataset Archive",
            "",
            "Archive Files (*.tar *.tar.gz *.tgz *.zip);;All Files (*)"
        )

        if archive_file:
            self.tps_viewmodel.load_dataset(archive_file)
            self.navigation_viewmodel.set_pinnacle_api(self.tps_viewmodel.pinnacle_api)

    def _update_slice_display(self, slice_index: int):
        """Update slice display in status bar."""
        if hasattr(self, 'status_bar'):
            total_slices = self.tps_viewmodel.totalSlices
            orientation = self.tps_viewmodel.currentOrientation
            self.status_bar.showMessage(
                f"{orientation.title()} - Slice {slice_index + 1}/{total_slices}"
            )

    def _show_error_message(self, error_message: str):
        """Show error message to user."""
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.critical(self, "Error", error_message)

    def _on_patient_selected(self, patient_info: dict):
        """Handle patient selection."""
        logger.info(f"Patient selected: {patient_info.get('name', 'Unknown')}")

    def _on_plan_selected(self, plan_info: dict):
        """Handle plan selection."""
        logger.info(f"Plan selected: {plan_info.get('name', 'Unknown')}")

    def _on_trial_selected(self, trial_info: dict):
        """Handle trial selection."""
        logger.info(f"Trial selected: {trial_info.get('name', 'Unknown')}")

        # Load overlays for the selected trial
        if self.tps_viewmodel.pinnacle_api:
            self.overlay_viewmodel.load_overlays(
                self.tps_viewmodel.pinnacle_api,
                trial_info.get('patient_dir', ''),
                trial_info.get('plan_dir', ''),
                trial_info.get('trial_dir', '')
            )

    def _on_overlay_visibility_changed(self, overlay_type: str, item_id: int, visible: bool):
        """Handle overlay visibility changes."""
        logger.debug(f"Overlay visibility changed: {overlay_type} {item_id} -> {visible}")
        # This will be connected to VTK actors in future phases

    def _auto_load_test_data(self):
        """Automatically load test data directory on startup."""
        # Get path to test data directory
        gui_dir = Path(__file__).parent
        project_root = gui_dir.parent.parent.parent
        test_data_path = project_root / "tests" / "data" / "01"

        if test_data_path.exists():
            logger.info(f"Auto-loading test data from: {test_data_path}")
            self.tps_viewmodel.load_dataset(str(test_data_path))
            self.navigation_viewmodel.set_pinnacle_api(self.tps_viewmodel.pinnacle_api)
        else:
            logger.warning(f"Test data path not found: {test_data_path}")

    def closeEvent(self, event):
        """Handle application close event."""
        logger.info("Application closing")
        event.accept()
