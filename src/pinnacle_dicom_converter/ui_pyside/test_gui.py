#!/usr/bin/env python3
"""
Test script for the PySide6 GUI implementation.

This script can be used to test the basic GUI functionality
during development.
"""

import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from pinnacle_dicom_converter.ui_nicegui_pyside.main import main

if __name__ == "__main__":
    print("Testing Pinnacle DICOM Converter GUI...")
    print("Close the window to exit the test.")
    sys.exit(main())
