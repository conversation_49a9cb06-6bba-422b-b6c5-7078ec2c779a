"""
VTK renderer pipeline for TPS visualization.

This module provides the core VTK rendering pipeline for medical imaging
with optimized orientation switching using vtkImageReslice.
"""

import logging
import numpy as np
from typing import Optional, Tuple

# VTK imports with specific modules for better performance
from vtkmodules.vtkCommonDataModel import vtkImageData
from vtkmodules.vtkImagingCore import vtkImageReslice
from vtkmodules.vtkRenderingCore import (
    vtkRenderer, vtkImageActor, vtkImageProperty, vtkCamera, vtkImageMapper
)

from pinnacle_dicom_converter.core.models.image_set import ImageSet

logger = logging.getLogger(__name__)


class TPSRenderer:
    """High-performance VTK renderer for TPS data visualization.
    
    This class manages the VTK rendering pipeline optimized for medical imaging
    with fast orientation switching and efficient data handling.
    
    Key Performance Features:
    - Single vtkImageReslice for all orientations (no data copying)
    - GPU-accelerated window/level adjustments
    - Optimized camera positioning for medical imaging
    - Efficient slice navigation
    """
    
    def __init__(self, renderer: vtkRenderer):
        """Initialize the TPS renderer.
        
        Args:
            renderer: VTK renderer instance from the widget
        """
        self.renderer = renderer
        self.image_data: Optional[vtkImageData] = None
        self.current_orientation = "axial"
        self.current_slice_index = 0
        self.total_slices = 0
        
        # Core VTK pipeline components
        self.image_reslice = vtkImageReslice()
        self.image_mapper = vtkImageMapper()
        self.image_actor = vtkImageActor()
        self.image_property = vtkImageProperty()
        
        # Image metadata
        self.spacing = (1.0, 1.0, 1.0)
        self.origin = (0.0, 0.0, 0.0)
        self.dimensions = (512, 512, 100)
        
        # Window/Level settings
        self.window_width = 1400
        self.window_level = 1000
        
        self._setup_pipeline()
        self._setup_camera()
        
        logger.info("TPS renderer initialized")
    
    def _setup_pipeline(self):
        """Set up the VTK rendering pipeline."""
        # Configure image reslice for performance
        self.image_reslice.SetInterpolationModeToLinear()
        self.image_reslice.SetOutputDimensionality(2)  # 2D slices
        self.image_reslice.AutoCropOutputOn()

        # Set up mapper - only connect when we have data
        # self.image_mapper.SetInputConnection(self.image_reslice.GetOutputPort())

        # Configure image property for medical imaging
        self.image_property.SetInterpolationTypeToLinear()
        self.image_property.SetColorWindow(self.window_width)
        self.image_property.SetColorLevel(self.window_level)

        # Set up actor - defer mapper connection until we have data
        self.image_actor.SetProperty(self.image_property)

        # Add to renderer
        self.renderer.AddActor(self.image_actor)
        self.renderer.SetBackground(0.0, 0.0, 0.0)  # Black background for medical imaging

        logger.debug("VTK pipeline configured")
    
    def _setup_camera(self):
        """Set up camera for medical imaging."""
        camera = self.renderer.GetActiveCamera()
        camera.ParallelProjectionOn()  # Orthographic projection for medical imaging
        camera.SetViewUp(0, -1, 0)  # Standard medical imaging orientation
        
    def set_image_data(self, image_set: ImageSet) -> bool:
        """Load CT image data into the VTK pipeline.
        
        Args:
            image_set: ImageSet with pixel_data (numpy array)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not hasattr(image_set, 'pixel_data') or image_set.pixel_data is None:
                logger.error("ImageSet has no pixel_data")
                return False
            
            # Convert numpy array to VTK image data
            pixel_data = image_set.pixel_data  # Shape: (Z, Y, X)
            self.dimensions = pixel_data.shape
            self.total_slices = self.dimensions[0]
            self.current_slice_index = self.total_slices // 2
            
            # Get spacing and origin from ImageSet
            self.spacing = (image_set.x_pixdim, image_set.y_pixdim, image_set.z_pixdim)
            self.origin = (image_set.x_start, image_set.y_start, image_set.z_start)
            
            # Create VTK image data
            self.image_data = vtkImageData()
            self.image_data.SetDimensions(self.dimensions[2], self.dimensions[1], self.dimensions[0])  # VTK uses (X,Y,Z)
            self.image_data.SetSpacing(self.spacing)
            self.image_data.SetOrigin(self.origin)
            
            # Convert numpy array to VTK format (flatten and ensure correct data type)
            # VTK expects data in X-Y-Z order, numpy is Z-Y-X
            vtk_data = np.transpose(pixel_data, (2, 1, 0)).flatten()
            
            # Ensure correct data type for VTK
            from vtkmodules.util.numpy_support import numpy_to_vtk
            from vtkmodules.vtkCommonCore import VTK_UNSIGNED_SHORT

            if pixel_data.dtype == np.uint16:
                vtk_array = numpy_to_vtk(vtk_data, deep=True, array_type=VTK_UNSIGNED_SHORT)
            else:
                # Convert to uint16 if needed
                vtk_data = vtk_data.astype(np.uint16)
                vtk_array = numpy_to_vtk(vtk_data, deep=True, array_type=VTK_UNSIGNED_SHORT)
            
            self.image_data.GetPointData().SetScalars(vtk_array)
            
            # Connect to reslice
            self.image_reslice.SetInputData(self.image_data)

            # Connect mapper to reslice now that we have data
            self.image_mapper.SetInputConnection(self.image_reslice.GetOutputPort())
            self.image_actor.SetMapper(self.image_mapper)

            # Set initial orientation
            self.set_orientation(self.current_orientation)
            
            logger.info(f"Image data loaded: {self.dimensions} voxels, spacing: {self.spacing}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set image data: {e}")
            return False
    
    def set_orientation(self, orientation: str) -> bool:
        """Set the viewing orientation using fast reslice matrix updates.
        
        Args:
            orientation: "axial", "sagittal", or "coronal"
            
        Returns:
            True if successful, False otherwise
        """
        if orientation not in ("axial", "sagittal", "coronal"):
            logger.error(f"Invalid orientation: {orientation}")
            return False
        
        if self.image_data is None:
            logger.warning("No image data loaded")
            return False
        
        try:
            self.current_orientation = orientation
            
            # Configure reslice matrix for orientation (performance-critical)
            if orientation == "axial":
                # Standard axial view (looking down Z-axis)
                self.image_reslice.SetResliceAxesDirectionCosines(
                    1, 0, 0,  # X direction (left-right)
                    0, 1, 0,  # Y direction (anterior-posterior)
                    0, 0, 1   # Z direction (superior-inferior)
                )
                # Set slice position
                z_pos = self.origin[2] + self.current_slice_index * self.spacing[2]
                self.image_reslice.SetResliceAxesOrigin(self.origin[0], self.origin[1], z_pos)
                
            elif orientation == "sagittal":
                # Sagittal view (looking from the side)
                self.image_reslice.SetResliceAxesDirectionCosines(
                    0, 0, 1,  # X direction (was Z - superior-inferior)
                    0, 1, 0,  # Y direction (unchanged - anterior-posterior)
                    1, 0, 0   # Z direction (was X - left-right)
                )
                # Set slice position
                x_pos = self.origin[0] + self.current_slice_index * self.spacing[0]
                self.image_reslice.SetResliceAxesOrigin(x_pos, self.origin[1], self.origin[2])
                
            elif orientation == "coronal":
                # Coronal view (looking from front/back)
                self.image_reslice.SetResliceAxesDirectionCosines(
                    1, 0, 0,  # X direction (unchanged - left-right)
                    0, 0, 1,  # Y direction (was Z - superior-inferior)
                    0, 1, 0   # Z direction (was Y - anterior-posterior)
                )
                # Set slice position
                y_pos = self.origin[1] + self.current_slice_index * self.spacing[1]
                self.image_reslice.SetResliceAxesOrigin(self.origin[0], y_pos, self.origin[2])
            
            # Update pipeline
            self.image_reslice.Update()
            
            # Reset camera for new orientation
            self._reset_camera_for_orientation()
            
            # Render
            self.renderer.GetRenderWindow().Render()
            
            logger.debug(f"Orientation changed to: {orientation}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set orientation {orientation}: {e}")
            return False
    
    def set_slice_index(self, slice_index: int) -> bool:
        """Navigate to a specific slice.
        
        Args:
            slice_index: Target slice index (0-based)
            
        Returns:
            True if successful, False otherwise
        """
        if self.image_data is None:
            return False
        
        # Get max slices for current orientation
        if self.current_orientation == "axial":
            max_slices = self.dimensions[0]  # Z dimension
        elif self.current_orientation == "sagittal":
            max_slices = self.dimensions[2]  # X dimension
        else:  # coronal
            max_slices = self.dimensions[1]  # Y dimension
        
        # Clamp slice index
        slice_index = max(0, min(slice_index, max_slices - 1))
        
        if slice_index != self.current_slice_index:
            self.current_slice_index = slice_index
            # Re-apply orientation to update slice position
            self.set_orientation(self.current_orientation)
            
        return True
    
    def navigate_slice(self, delta: int) -> bool:
        """Navigate by a relative number of slices.
        
        Args:
            delta: Number of slices to move (positive or negative)
            
        Returns:
            True if successful, False otherwise
        """
        return self.set_slice_index(self.current_slice_index + delta)
    
    def set_window_level(self, window_width: int, window_level: int):
        """Update window/level settings for CT display.
        
        Args:
            window_width: Window width (contrast range)
            window_level: Window level (center value)
        """
        self.window_width = window_width
        self.window_level = window_level
        
        self.image_property.SetColorWindow(window_width)
        self.image_property.SetColorLevel(window_level)
        
        self.renderer.GetRenderWindow().Render()
        
        logger.debug(f"Window/Level updated: {window_width}/{window_level}")
    
    def _reset_camera_for_orientation(self):
        """Reset camera position and zoom for current orientation."""
        if self.image_data is None:
            return
        
        camera = self.renderer.GetActiveCamera()
        
        # Get image bounds
        bounds = self.image_reslice.GetOutput().GetBounds()
        
        # Calculate center point
        center_x = (bounds[0] + bounds[1]) / 2.0
        center_y = (bounds[2] + bounds[3]) / 2.0
        center_z = (bounds[4] + bounds[5]) / 2.0
        
        # Set camera position based on orientation
        if self.current_orientation == "axial":
            camera.SetPosition(center_x, center_y, center_z + 1000)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, -1, 0)
        elif self.current_orientation == "sagittal":
            camera.SetPosition(center_x + 1000, center_y, center_z)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 0, 1)
        else:  # coronal
            camera.SetPosition(center_x, center_y + 1000, center_z)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 0, 1)
        
        # Reset zoom to fit image
        self.renderer.ResetCamera()
    
    def get_current_slice_info(self) -> dict:
        """Get information about the current slice.
        
        Returns:
            Dictionary with slice information
        """
        if self.current_orientation == "axial":
            max_slices = self.dimensions[0]
        elif self.current_orientation == "sagittal":
            max_slices = self.dimensions[2]
        else:  # coronal
            max_slices = self.dimensions[1]
        
        return {
            'orientation': self.current_orientation,
            'slice_index': self.current_slice_index,
            'total_slices': max_slices,
            'window_width': self.window_width,
            'window_level': self.window_level
        }
