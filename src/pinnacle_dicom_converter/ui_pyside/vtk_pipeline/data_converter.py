"""
Data conversion utilities for converting Pinnacle models to VTK data structures.

This module provides efficient conversion functions for transforming
Pinnacle TPS data into VTK-compatible formats for rendering.
"""

import logging
import numpy as np
from typing import List, Optional, Tuple

# VTK imports
from vtkmodules.vtkCommonDataModel import vtkImageData, vtkPolyData, vtkCellArray
from vtkmodules.vtkCommonCore import vtkPoints, VTK_UNSIGNED_SHORT, VTK_FLOAT
from vtkmodules.util.numpy_support import numpy_to_vtk

from pinnacle_dicom_converter.core.models.image_set import ImageSet
from pinnacle_dicom_converter.core.models.roi import ROI
from pinnacle_dicom_converter.core.models.point import Point

logger = logging.getLogger(__name__)


class VTKDataConverter:
    """Utility class for converting Pinnacle data to VTK formats.
    
    This class provides static methods for efficient conversion of
    Pinnacle TPS data structures to VTK data objects with zero-copy
    optimization where possible.
    """
    
    @staticmethod
    def image_set_to_vtk_image_data(image_set: ImageSet) -> Optional[vtkImageData]:
        """Convert ImageSet to VTK ImageData.
        
        Args:
            image_set: Pinnacle ImageSet with pixel_data
            
        Returns:
            VTK ImageData object or None if conversion fails
        """
        try:
            if not hasattr(image_set, 'pixel_data') or image_set.pixel_data is None:
                logger.error("ImageSet has no pixel_data")
                return None
            
            pixel_data = image_set.pixel_data  # Shape: (Z, Y, X)
            dimensions = pixel_data.shape
            
            # Create VTK image data
            vtk_image = vtkImageData()
            
            # Set dimensions (VTK uses X, Y, Z order)
            vtk_image.SetDimensions(dimensions[2], dimensions[1], dimensions[0])
            
            # Set spacing
            spacing = (image_set.x_pixdim, image_set.y_pixdim, image_set.z_pixdim)
            vtk_image.SetSpacing(spacing)
            
            # Set origin
            origin = (image_set.x_start, image_set.y_start, image_set.z_start)
            vtk_image.SetOrigin(origin)
            
            # Convert numpy array to VTK format
            # VTK expects data in X-Y-Z order, numpy is Z-Y-X
            vtk_data = np.transpose(pixel_data, (2, 1, 0)).flatten()
            
            # Ensure correct data type
            if pixel_data.dtype != np.uint16:
                vtk_data = vtk_data.astype(np.uint16)
            
            # Create VTK array with zero-copy if possible
            vtk_array = numpy_to_vtk(vtk_data, deep=True, array_type=VTK_UNSIGNED_SHORT)
            vtk_image.GetPointData().SetScalars(vtk_array)
            
            logger.debug(f"Converted ImageSet to VTK: {dimensions} -> {vtk_image.GetDimensions()}")
            return vtk_image
            
        except Exception as e:
            logger.error(f"Failed to convert ImageSet to VTK: {e}")
            return None
    
    @staticmethod
    def roi_contours_to_vtk_polydata(roi: ROI) -> Optional[vtkPolyData]:
        """Convert ROI contours to VTK PolyData.
        
        Args:
            roi: Pinnacle ROI with contour data
            
        Returns:
            VTK PolyData object or None if conversion fails
        """
        try:
            if not hasattr(roi, 'contour_list') or not roi.contour_list:
                logger.warning(f"ROI {roi.roi_id} has no contours")
                return None
            
            # Create VTK objects
            poly_data = vtkPolyData()
            points = vtkPoints()
            lines = vtkCellArray()
            
            point_id = 0
            
            # Process each contour
            for contour in roi.contour_list:
                if not contour or len(contour) < 3:  # Need at least 3 points for a contour
                    continue
                
                # Add points for this contour
                contour_start_id = point_id
                
                for point in contour:
                    # Point should be (x, y, z) in mm
                    if len(point) >= 3:
                        points.InsertNextPoint(point[0], point[1], point[2])
                        point_id += 1
                
                # Create line cells for this contour
                if point_id > contour_start_id:
                    contour_size = point_id - contour_start_id
                    
                    # Create line strip for contour
                    lines.InsertNextCell(contour_size + 1)  # +1 to close the contour
                    for i in range(contour_size):
                        lines.InsertCellPoint(contour_start_id + i)
                    # Close the contour
                    lines.InsertCellPoint(contour_start_id)
            
            # Set data
            poly_data.SetPoints(points)
            poly_data.SetLines(lines)
            
            logger.debug(f"Converted ROI {roi.roi_id} to VTK: {points.GetNumberOfPoints()} points, {lines.GetNumberOfCells()} contours")
            return poly_data
            
        except Exception as e:
            logger.error(f"Failed to convert ROI {roi.roi_id} to VTK: {e}")
            return None
    
    @staticmethod
    def points_to_vtk_polydata(points: List[Point]) -> Optional[vtkPolyData]:
        """Convert POI list to VTK PolyData.
        
        Args:
            points: List of Pinnacle Point objects
            
        Returns:
            VTK PolyData object or None if conversion fails
        """
        try:
            if not points:
                logger.warning("No points to convert")
                return None
            
            # Create VTK objects
            poly_data = vtkPolyData()
            vtk_points = vtkPoints()
            vertices = vtkCellArray()
            
            # Add each point
            for i, point in enumerate(points):
                # Point coordinates in mm
                x, y, z = point.x, point.y, point.z
                vtk_points.InsertNextPoint(x, y, z)
                
                # Create vertex cell
                vertices.InsertNextCell(1)
                vertices.InsertCellPoint(i)
            
            # Set data
            poly_data.SetPoints(vtk_points)
            poly_data.SetVerts(vertices)
            
            logger.debug(f"Converted {len(points)} points to VTK")
            return poly_data
            
        except Exception as e:
            logger.error(f"Failed to convert points to VTK: {e}")
            return None
    
    @staticmethod
    def dose_grid_to_vtk_image_data(dose_data: np.ndarray, 
                                   spacing: Tuple[float, float, float],
                                   origin: Tuple[float, float, float]) -> Optional[vtkImageData]:
        """Convert dose grid to VTK ImageData.
        
        Args:
            dose_data: 3D numpy array with dose values
            spacing: Voxel spacing (x, y, z) in mm
            origin: Grid origin (x, y, z) in mm
            
        Returns:
            VTK ImageData object or None if conversion fails
        """
        try:
            if dose_data is None or dose_data.size == 0:
                logger.warning("No dose data to convert")
                return None
            
            # Create VTK image data
            vtk_image = vtkImageData()
            
            # Set dimensions (VTK uses X, Y, Z order)
            dimensions = dose_data.shape
            vtk_image.SetDimensions(dimensions[2], dimensions[1], dimensions[0])
            
            # Set spacing and origin
            vtk_image.SetSpacing(spacing)
            vtk_image.SetOrigin(origin)
            
            # Convert numpy array to VTK format
            # VTK expects data in X-Y-Z order, numpy is Z-Y-X
            vtk_data = np.transpose(dose_data, (2, 1, 0)).flatten()
            
            # Convert to float32 for dose data
            if vtk_data.dtype != np.float32:
                vtk_data = vtk_data.astype(np.float32)
            
            # Create VTK array
            vtk_array = numpy_to_vtk(vtk_data, deep=True, array_type=VTK_FLOAT)
            vtk_image.GetPointData().SetScalars(vtk_array)
            
            logger.debug(f"Converted dose grid to VTK: {dimensions}")
            return vtk_image
            
        except Exception as e:
            logger.error(f"Failed to convert dose grid to VTK: {e}")
            return None
    
    @staticmethod
    def beam_lines_to_vtk_polydata(beam_geometry: dict) -> Optional[vtkPolyData]:
        """Convert beam geometry to VTK PolyData lines.
        
        Args:
            beam_geometry: Dictionary with beam line coordinates
            
        Returns:
            VTK PolyData object or None if conversion fails
        """
        try:
            # This is a placeholder for beam geometry conversion
            # The actual implementation would depend on the beam data structure
            
            # Create VTK objects
            poly_data = vtkPolyData()
            points = vtkPoints()
            lines = vtkCellArray()
            
            # Example: Create a simple beam line from isocenter to source
            if 'isocenter' in beam_geometry and 'source_position' in beam_geometry:
                iso = beam_geometry['isocenter']
                source = beam_geometry['source_position']
                
                # Add points
                points.InsertNextPoint(iso[0], iso[1], iso[2])
                points.InsertNextPoint(source[0], source[1], source[2])
                
                # Create line
                lines.InsertNextCell(2)
                lines.InsertCellPoint(0)
                lines.InsertCellPoint(1)
                
                # Set data
                poly_data.SetPoints(points)
                poly_data.SetLines(lines)
                
                logger.debug("Converted beam geometry to VTK")
                return poly_data
            
            logger.warning("Insufficient beam geometry data")
            return None
            
        except Exception as e:
            logger.error(f"Failed to convert beam geometry to VTK: {e}")
            return None
