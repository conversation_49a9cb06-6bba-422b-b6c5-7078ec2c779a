"""
VTK actor management for TPS visualization.

This module provides utilities for creating and managing VTK actors
for different types of TPS data (ROIs, POIs, beams, dose).
"""

import logging
from typing import Dict, List, Optional, Tuple

# VTK imports
from vtkmodules.vtkRenderingCore import (
    vtkActor, vtkPolyDataMapper, vtkProperty, vtkRenderer
)
from vtkmodules.vtkFiltersCore import vtkContourFilter
from vtkmodules.vtkFiltersGeneral import vtkShrinkPolyData
from vtkmodules.vtkFiltersSources import vtkSphereSource
from vtkmodules.vtkFiltersCore import vtkGlyph3D
from vtkmodules.vtkCommonDataModel import vtkPolyData

logger = logging.getLogger(__name__)


class ActorManager:
    """Manager for VTK actors in the TPS viewer.
    
    This class handles creation, management, and updates of VTK actors
    for different types of TPS data with efficient visibility and
    property management.
    """
    
    def __init__(self, renderer: vtkRenderer):
        """Initialize the actor manager.
        
        Args:
            renderer: VTK renderer to manage actors for
        """
        self.renderer = renderer
        
        # Actor storage by type and ID
        self.roi_actors: Dict[int, vtkActor] = {}
        self.poi_actors: Dict[int, vtkActor] = {}
        self.beam_actors: Dict[int, vtkActor] = {}
        self.dose_actors: Dict[str, vtkActor] = {}  # Key by dose level
        
        # Default colors
        self.default_roi_colors = [
            (1.0, 0.0, 0.0),  # Red
            (0.0, 1.0, 0.0),  # Green
            (0.0, 0.0, 1.0),  # Blue
            (1.0, 1.0, 0.0),  # Yellow
            (1.0, 0.0, 1.0),  # Magenta
            (0.0, 1.0, 1.0),  # Cyan
            (1.0, 0.5, 0.0),  # Orange
            (0.5, 0.0, 1.0),  # Purple
        ]
        
        logger.info("Actor manager initialized")
    
    def create_roi_actor(self, roi_id: int, poly_data: vtkPolyData, 
                        color: Optional[Tuple[float, float, float]] = None,
                        line_width: float = 2.0) -> Optional[vtkActor]:
        """Create a VTK actor for ROI contours.
        
        Args:
            roi_id: ROI identifier
            poly_data: VTK PolyData with contour lines
            color: RGB color tuple (0-1 range), auto-assigned if None
            line_width: Line width for contours
            
        Returns:
            VTK actor or None if creation fails
        """
        try:
            # Create mapper
            mapper = vtkPolyDataMapper()
            mapper.SetInputData(poly_data)
            
            # Create actor
            actor = vtkActor()
            actor.SetMapper(mapper)
            
            # Set properties
            prop = actor.GetProperty()
            prop.SetRepresentationToWireframe()
            prop.SetLineWidth(line_width)
            
            # Set color
            if color is None:
                color_index = roi_id % len(self.default_roi_colors)
                color = self.default_roi_colors[color_index]
            
            prop.SetColor(color[0], color[1], color[2])
            
            # Store and add to renderer
            self.roi_actors[roi_id] = actor
            self.renderer.AddActor(actor)
            
            logger.debug(f"Created ROI actor for ROI {roi_id}")
            return actor
            
        except Exception as e:
            logger.error(f"Failed to create ROI actor for ROI {roi_id}: {e}")
            return None
    
    def create_poi_actor(self, poi_id: int, poly_data: vtkPolyData,
                        color: Tuple[float, float, float] = (1.0, 1.0, 0.0),
                        sphere_radius: float = 2.0) -> Optional[vtkActor]:
        """Create a VTK actor for POI markers.
        
        Args:
            poi_id: POI identifier
            poly_data: VTK PolyData with point vertices
            color: RGB color tuple (0-1 range)
            sphere_radius: Radius of sphere markers in mm
            
        Returns:
            VTK actor or None if creation fails
        """
        try:
            # Create sphere source for markers
            sphere = vtkSphereSource()
            sphere.SetRadius(sphere_radius)
            sphere.SetPhiResolution(8)
            sphere.SetThetaResolution(8)
            
            # Create glyph filter to place spheres at points
            glyph = vtkGlyph3D()
            glyph.SetInputData(poly_data)
            glyph.SetSourceConnection(sphere.GetOutputPort())
            glyph.SetScaleModeToDataScalingOff()
            
            # Create mapper
            mapper = vtkPolyDataMapper()
            mapper.SetInputConnection(glyph.GetOutputPort())
            
            # Create actor
            actor = vtkActor()
            actor.SetMapper(mapper)
            
            # Set properties
            prop = actor.GetProperty()
            prop.SetColor(color[0], color[1], color[2])
            prop.SetSpecular(0.3)
            prop.SetSpecularPower(20)
            
            # Store and add to renderer
            self.poi_actors[poi_id] = actor
            self.renderer.AddActor(actor)
            
            logger.debug(f"Created POI actor for POI {poi_id}")
            return actor
            
        except Exception as e:
            logger.error(f"Failed to create POI actor for POI {poi_id}: {e}")
            return None
    
    def create_beam_actor(self, beam_id: int, poly_data: vtkPolyData,
                         color: Tuple[float, float, float] = (0.0, 1.0, 1.0),
                         line_width: float = 3.0) -> Optional[vtkActor]:
        """Create a VTK actor for beam lines.
        
        Args:
            beam_id: Beam identifier
            poly_data: VTK PolyData with beam lines
            color: RGB color tuple (0-1 range)
            line_width: Line width for beam visualization
            
        Returns:
            VTK actor or None if creation fails
        """
        try:
            # Create mapper
            mapper = vtkPolyDataMapper()
            mapper.SetInputData(poly_data)
            
            # Create actor
            actor = vtkActor()
            actor.SetMapper(mapper)
            
            # Set properties
            prop = actor.GetProperty()
            prop.SetRepresentationToWireframe()
            prop.SetLineWidth(line_width)
            prop.SetColor(color[0], color[1], color[2])
            
            # Store and add to renderer
            self.beam_actors[beam_id] = actor
            self.renderer.AddActor(actor)
            
            logger.debug(f"Created beam actor for beam {beam_id}")
            return actor
            
        except Exception as e:
            logger.error(f"Failed to create beam actor for beam {beam_id}: {e}")
            return None
    
    def create_dose_contour_actor(self, dose_level: str, contour_filter: vtkContourFilter,
                                 color: Tuple[float, float, float],
                                 opacity: float = 0.7) -> Optional[vtkActor]:
        """Create a VTK actor for dose isodose contours.
        
        Args:
            dose_level: Dose level identifier (e.g., "95%", "50%")
            contour_filter: VTK contour filter with dose data
            color: RGB color tuple (0-1 range)
            opacity: Opacity value (0-1 range)
            
        Returns:
            VTK actor or None if creation fails
        """
        try:
            # Create mapper
            mapper = vtkPolyDataMapper()
            mapper.SetInputConnection(contour_filter.GetOutputPort())
            
            # Create actor
            actor = vtkActor()
            actor.SetMapper(mapper)
            
            # Set properties
            prop = actor.GetProperty()
            prop.SetColor(color[0], color[1], color[2])
            prop.SetOpacity(opacity)
            prop.SetRepresentationToWireframe()
            prop.SetLineWidth(2.0)
            
            # Store and add to renderer
            self.dose_actors[dose_level] = actor
            self.renderer.AddActor(actor)
            
            logger.debug(f"Created dose contour actor for {dose_level}")
            return actor
            
        except Exception as e:
            logger.error(f"Failed to create dose contour actor for {dose_level}: {e}")
            return None
    
    def set_roi_visibility(self, roi_id: int, visible: bool):
        """Set visibility of an ROI actor.
        
        Args:
            roi_id: ROI identifier
            visible: Visibility state
        """
        if roi_id in self.roi_actors:
            self.roi_actors[roi_id].SetVisibility(visible)
            logger.debug(f"ROI {roi_id} visibility set to {visible}")
    
    def set_poi_visibility(self, poi_id: int, visible: bool):
        """Set visibility of a POI actor.
        
        Args:
            poi_id: POI identifier
            visible: Visibility state
        """
        if poi_id in self.poi_actors:
            self.poi_actors[poi_id].SetVisibility(visible)
            logger.debug(f"POI {poi_id} visibility set to {visible}")
    
    def set_beam_visibility(self, beam_id: int, visible: bool):
        """Set visibility of a beam actor.
        
        Args:
            beam_id: Beam identifier
            visible: Visibility state
        """
        if beam_id in self.beam_actors:
            self.beam_actors[beam_id].SetVisibility(visible)
            logger.debug(f"Beam {beam_id} visibility set to {visible}")
    
    def set_dose_visibility(self, dose_level: str, visible: bool):
        """Set visibility of a dose contour actor.
        
        Args:
            dose_level: Dose level identifier
            visible: Visibility state
        """
        if dose_level in self.dose_actors:
            self.dose_actors[dose_level].SetVisibility(visible)
            logger.debug(f"Dose {dose_level} visibility set to {visible}")
    
    def set_roi_color(self, roi_id: int, color: Tuple[float, float, float]):
        """Change the color of an ROI actor.
        
        Args:
            roi_id: ROI identifier
            color: RGB color tuple (0-1 range)
        """
        if roi_id in self.roi_actors:
            self.roi_actors[roi_id].GetProperty().SetColor(color[0], color[1], color[2])
            logger.debug(f"ROI {roi_id} color changed to {color}")
    
    def remove_roi_actor(self, roi_id: int):
        """Remove an ROI actor from the renderer.
        
        Args:
            roi_id: ROI identifier
        """
        if roi_id in self.roi_actors:
            self.renderer.RemoveActor(self.roi_actors[roi_id])
            del self.roi_actors[roi_id]
            logger.debug(f"ROI actor {roi_id} removed")
    
    def remove_poi_actor(self, poi_id: int):
        """Remove a POI actor from the renderer.
        
        Args:
            poi_id: POI identifier
        """
        if poi_id in self.poi_actors:
            self.renderer.RemoveActor(self.poi_actors[poi_id])
            del self.poi_actors[poi_id]
            logger.debug(f"POI actor {poi_id} removed")
    
    def remove_beam_actor(self, beam_id: int):
        """Remove a beam actor from the renderer.
        
        Args:
            beam_id: Beam identifier
        """
        if beam_id in self.beam_actors:
            self.renderer.RemoveActor(self.beam_actors[beam_id])
            del self.beam_actors[beam_id]
            logger.debug(f"Beam actor {beam_id} removed")
    
    def remove_dose_actor(self, dose_level: str):
        """Remove a dose contour actor from the renderer.
        
        Args:
            dose_level: Dose level identifier
        """
        if dose_level in self.dose_actors:
            self.renderer.RemoveActor(self.dose_actors[dose_level])
            del self.dose_actors[dose_level]
            logger.debug(f"Dose actor {dose_level} removed")
    
    def clear_all_actors(self):
        """Remove all managed actors from the renderer."""
        # Remove all ROI actors
        for roi_id in list(self.roi_actors.keys()):
            self.remove_roi_actor(roi_id)
        
        # Remove all POI actors
        for poi_id in list(self.poi_actors.keys()):
            self.remove_poi_actor(poi_id)
        
        # Remove all beam actors
        for beam_id in list(self.beam_actors.keys()):
            self.remove_beam_actor(beam_id)
        
        # Remove all dose actors
        for dose_level in list(self.dose_actors.keys()):
            self.remove_dose_actor(dose_level)
        
        logger.info("All actors cleared")
    
    def get_actor_counts(self) -> Dict[str, int]:
        """Get counts of actors by type.
        
        Returns:
            Dictionary with actor counts
        """
        return {
            'rois': len(self.roi_actors),
            'pois': len(self.poi_actors),
            'beams': len(self.beam_actors),
            'dose_levels': len(self.dose_actors)
        }
