"""
Command-line interface for Pinnacle DICOM Converter.

This module provides a comprehensive CLI for exploring and exporting Pinnacle
treatment planning data to DICOM format. The CLI includes commands for:

- Listing patients, plans, and trials in archives
- Displaying detailed trial information
- Exporting data to DICOM format with flexible filtering
"""

import click

from pinnacle_dicom_converter.cli.commands.list_patients import list_patients
from pinnacle_dicom_converter.cli.commands.list_plans import list_plans
from pinnacle_dicom_converter.cli.commands.list_trials import list_trials
from pinnacle_dicom_converter.cli.commands.trial_details import trial_details
from pinnacle_dicom_converter.cli.commands.export import export


@click.group()
@click.version_option()
def cli():
    """
    Pinnacle DICOM Converter - Explore and convert Pinnacle TPS data to DICOM format.

    This tool provides commands for exploring Pinnacle treatment planning archives
    and exporting data to DICOM format. Use the individual commands to:

    \b
    • list-patients: Show all patients in an archive
    • list-plans: Show plans for a specific patient
    • list-trials: Show trials for a specific plan
    • trial-details: Show detailed information about a trial
    • export: Convert data to DICOM format

    Examples:
        # Explore archive contents
        pinnacle-dicom-converter list-patients --input /path/to/archive

        # Export specific patient data
        pinnacle-dicom-converter export --input archive.tar.gz --output ./dicom --patient-id 123

    For help with individual commands, use:
        pinnacle-dicom-converter COMMAND --help
    """
    pass


# Add all the new commands
cli.add_command(list_patients, name="list-patients")
cli.add_command(list_plans, name="list-plans")
cli.add_command(list_trials, name="list-trials")
cli.add_command(trial_details, name="trial-details")
cli.add_command(export)


@cli.command()
@click.option(
    "--port",
    default=8080,
    type=int,
    help="Port number for the web server (default: 8080)",
)
@click.option(
    "--no-browser",
    is_flag=True,
    help="Don't automatically open browser window",
)
@click.option(
    "--reload",
    is_flag=True,
    help="Enable auto-reload on code changes (development mode)",
)
def gui(port: int, no_browser: bool, reload: bool):
    """Launch the NiceGUI-based desktop GUI application.

    The GUI provides an interactive interface for:
    - Browsing patients, plans, and trials
    - Viewing CT images with ROI overlays
    - Configuring and exporting DICOM data

    The application will open in your default web browser. For native
    desktop mode, use the packaged executable instead.

    Examples:
        # Launch GUI on default port
        pinnacle-dicom-converter gui

        # Launch on custom port without opening browser
        pinnacle-dicom-converter gui --port 9000 --no-browser

        # Development mode with auto-reload
        pinnacle-dicom-converter gui --reload
    """
    click.echo("Starting Pinnacle DICOM Converter GUI...")
    click.echo(f"Server will run on http://localhost:{port}")

    try:
        from pinnacle_dicom_converter.ui_nicegui import main as ui_main

        ui_main.run(
            port=port,
            show=not no_browser,
            reload=reload,
        )
    except ImportError as e:
        click.echo(f"Error: Failed to import NiceGUI components: {e}", err=True)
        click.echo("Please ensure NiceGUI is installed: pip install nicegui", err=True)
    except Exception as e:
        click.echo(f"Error launching GUI: {e}", err=True)


@cli.command()
def desktop():
    """Launch the PySide6-based native desktop GUI application.

    The desktop GUI provides a high-performance native interface for:
    - Browsing patients, plans, and trials
    - Viewing CT images with VTK-powered visualization
    - Configuring and exporting DICOM data

    This is a native Qt application with better performance than the web-based GUI.

    Examples:
        # Launch desktop GUI
        pinnacle-dicom-converter desktop
    """
    click.echo("Starting Pinnacle DICOM Converter Desktop GUI...")

    try:
        from pinnacle_dicom_converter.ui_nicegui_pyside.main import main as gui_main

        exit_code = gui_main()
        raise SystemExit(exit_code)
    except ImportError as e:
        click.echo(f"Error: Failed to import PySide6 components: {e}", err=True)
        click.echo("Please ensure PySide6 is installed: pip install pyside6", err=True)
        raise SystemExit(1)
    except Exception as e:
        click.echo(f"Error launching desktop GUI: {e}", err=True)
        raise SystemExit(1)


if __name__ in {"__main__", "__mp_main__"}: # multiprocessing required for NiceGUI
    cli()