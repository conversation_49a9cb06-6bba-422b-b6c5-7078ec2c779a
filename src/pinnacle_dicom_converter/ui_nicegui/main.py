"""Main entry point for Pinnacle DICOM Converter NiceGUI application.

This module initializes the NiceGUI web application with dark theme and
provides the main application page structure.
"""

from nicegui import ui

from .styles.theme import apply_dark_theme
from .views.main_view import create_main_view

# Initialize dark mode globally
dark = ui.dark_mode()
dark.enable()


def init_app() -> None:
    """Initialize the NiceGUI application with configuration and theme.

    Sets up dark mode, applies custom theme, and configures application
    storage for persistent settings across sessions.
    """
    # Configuration initialization only
    # Component-specific styles will be added when building those components
    pass


# Call init during module import
init_app()


@ui.page("/")
def main_page():
    """Main application page with complete layout.

    Delegates to create_main_view() for the actual view construction
    and ViewModel wiring.
    """
    apply_dark_theme()
    create_main_view()


def run(
    *,
    title: str = "Pinnacle DICOM Converter",
    favicon: str = "🏥",
    port: int = 8080,
    reload: bool = False,
    show: bool = True,
    native: bool = False,
) -> None:
    """Run the NiceGUI application.

    Args:
        title: Application window title
        favicon: Application favicon (emoji or path to image)
        port: Port number for the web server
        reload: Enable auto-reload on code changes (development mode)
        show: Automatically open browser window
        native: Run in native desktop mode (requires native packaging)
    """
    # Start the NiceGUI server (theme already initialized at module level)
    ui.run(
        title=title,
        favicon=favicon,
        port=port,
        dark=True,
        reload=reload,
        show=show,
        native=native,
    )


if __name__ in {"__main__", "__mp_main__"}:
    # Run in development mode with auto-reload
    run(reload=True, show=True)