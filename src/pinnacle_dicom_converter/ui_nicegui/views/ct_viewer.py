"""CT Viewer component for central image display.

This module provides the main CT image viewer with orientation controls,
GPU-accelerated WebGL rendering, and navigation hints. It uses a custom
Vue component for high-performance client-side rendering.
"""

from nicegui import ui

from pinnacle_dicom_converter.ui_nicegui.viewmodels.image_viewmodel import ImageViewModel
from pinnacle_dicom_converter.ui_nicegui.components.ct_viewer import CTViewer


def create_ct_viewer(image_vm: ImageViewModel) -> None:
    """Create the central CT image viewer.
    
    Args:
        image_vm: ImageViewModel instance for state management
    """
    with ui.column().classes("w-full h-[calc(100vh-59px)] bg-black"):
        # Title bar with orientation controls
        with ui.row().classes("w-full items-center justify-between px-4 py-2 bg-gray-900"):
            ui.label("CT Viewer").classes("text-h6 font-medium flex-1")

            # Image info (center)
            image_info_label = ui.label("No image loaded").classes("text-sm text-gray-400 flex-1")
            image_info_label.bind_text_from(image_vm, "image_info_text")

            # Orientation controls (right)
            with ui.row().classes("gap-2 flex-1 justify-end"):
                orientation_buttons = {}
                for orientation in ["Axial", "Sagittal", "Coronal"]:
                    btn = ui.label(orientation).classes("text-sm cursor-pointer px-2 py-1 rounded")
                    btn.on("click", lambda o=orientation: image_vm.set_orientation(str(o).lower()))
                    orientation_buttons[orientation.lower()] = btn

                    if orientation != "Coronal":
                        ui.label("|").classes("text-gray-600")

                # Function to update orientation button styles
                def update_orientation_styles():
                    for orient, button in orientation_buttons.items():
                        if image_vm.current_orientation == orient:
                            button.classes("text-blue-400 bg-blue-900/30 underline", remove="text-gray-400 hover:text-gray-300")
                        else:
                            button.classes("text-gray-400 hover:text-gray-300", remove="text-blue-400 bg-blue-900/30 underline")

                # Initial styling
                update_orientation_styles()

                # Store the style update function for later use
                image_vm._update_orientation_styles = update_orientation_styles

        # Main CT image viewer - Custom Vue component with WebGL rendering
        ct_viewer = CTViewer(
            initial_slice_index=image_vm.current_slice_index,
            initial_orientation=image_vm.current_orientation,
            initial_window_width=image_vm.window_width,
            initial_window_level=image_vm.window_level
        ).classes("flex-1 w-full")

        # Wire up event handlers
        ct_viewer.on_slice_changed = image_vm.on_slice_changed_from_vue
        ct_viewer.on_mouse_move = image_vm.on_mouse_move_from_vue
        ct_viewer.on_data_loaded = image_vm.on_data_loaded_from_vue

        # Store reference in ViewModel for updates
        image_vm.ct_viewer_component = ct_viewer

        # Bottom controls bar
        with ui.row().classes("w-full items-center justify-between px-4 py-2 bg-gray-900"):
            # Navigation hints (left)
            ui.label(
                "Navigation: ↑↓ (1 slice), PgUp/PgDn (5 slices), Mouse wheel | "
                "Zoom: +/-, Home=Reset | Coordinates: Mouse position"
            ).classes("text-xs text-gray-400")

            # Coordinate display (right)
            coord_label = ui.label("").classes("text-xs text-gray-300")
            coord_label.bind_text_from(image_vm, "coordinate_text")


def setup_refresh_callbacks(image_vm: ImageViewModel) -> None:
    """Set up refresh callbacks for the CT viewer.

    This function is called after the CT viewer is created to set up any
    necessary refresh callbacks. Currently, the CT viewer uses direct
    event handlers from the Vue component, so no additional setup is needed.

    Args:
        image_vm: ImageViewModel instance for state management
    """
    # No additional refresh callbacks needed for CT viewer
    # The Vue component handles all updates through direct event handlers
    pass
