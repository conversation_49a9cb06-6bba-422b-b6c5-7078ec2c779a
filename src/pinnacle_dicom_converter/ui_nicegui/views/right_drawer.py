"""Right drawer view component for control panels."""

from nicegui import ui

from pinnacle_dicom_converter.ui_nicegui.viewmodels.navigation_viewmodel import NavigationViewModel
from pinnacle_dicom_converter.ui_nicegui.viewmodels.image_viewmodel import ImageViewModel
from pinnacle_dicom_converter.ui_nicegui.viewmodels.overlay_viewmodel import OverlayViewModel
from pinnacle_dicom_converter.ui_nicegui.views.panels import ct_panel, roi_panel, poi_panel, beam_panel, dose_panel


def create_right_drawer(
    nav_vm: NavigationViewModel,
    image_vm: ImageViewModel,
    overlay_vm: OverlayViewModel,
) -> None:
    """Create right drawer with control panels.

    Creates a right drawer containing:
    - Current selection information card
    - Tabbed control panels (CT, ROIs, POIs, Beams, Dose)

    Args:
        nav_vm: NavigationViewModel for displaying current selection
        image_vm: ImageViewModel for CT controls
        overlay_vm: OverlayViewModel for overlay controls
    """
    with ui.right_drawer(fixed=True).classes("bg_primary").props("width=400") as drawer:
        drawer.show()  # Always visible

        with ui.column().classes("w-full h-full"):
            # Current selection info card
            # with ui.card().classes("w-full bg_secondary mx-4 mt-4 mb-2"):
            with ui.column().classes("w-full"):
                ui.label("Trial Information").classes("text-h6 font-medium mb-2")

                # Patient selection
                ui.label().classes("text-sm").bind_text_from(nav_vm, "patient_label_text")

                # Plan selection
                ui.label().classes("text-sm").bind_text_from(nav_vm, "plan_label_text")

                # Trial selection
                ui.label().classes("text-sm").bind_text_from(nav_vm, "trial_label_text")

            ui.separator()
            
            # Tabbed panels
            with ui.tabs().classes("w-full").props("dense") as tabs:
                ct_tab = ui.tab("CT", icon="image")
                roi_tab = ui.tab("ROIs", icon="polyline")
                poi_tab = ui.tab("POIs", icon="place")
                beam_tab = ui.tab("Beams", icon="straighten")
                dose_tab = ui.tab("Dose", icon="gradient")

            with ui.tab_panels(tabs, value=ct_tab).classes(
                "w-full flex-1 overflow-auto"
            ):
                with ui.tab_panel(ct_tab).classes("p-0"):
                    ct_panel.create_ct_panel(image_vm)

                with ui.tab_panel(roi_tab).classes("p-0"):
                    roi_panel.create_roi_panel(overlay_vm)

                with ui.tab_panel(poi_tab).classes("p-0"):
                    poi_panel.create_poi_panel(overlay_vm)

                with ui.tab_panel(beam_tab).classes("p-0"):
                    beam_panel.create_beam_panel(overlay_vm)

                with ui.tab_panel(dose_tab).classes("p-0"):
                    dose_panel.create_dose_panel(overlay_vm)


def setup_refresh_callbacks(overlay_vm: OverlayViewModel) -> None:
    """Set up refresh callbacks for all overlay panels.

    This connects the ViewModel's refresh methods to the UI refreshable functions.

    Args:
        overlay_vm: OverlayViewModel instance
    """
    roi_panel.setup_refresh_callback(overlay_vm)
    poi_panel.setup_refresh_callback(overlay_vm)
    beam_panel.setup_refresh_callback(overlay_vm)
    dose_panel.setup_refresh_callback(overlay_vm)
