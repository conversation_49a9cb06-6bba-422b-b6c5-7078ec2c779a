"""Left drawer view component for patient/plan/trial navigation."""

from nicegui import ui

from ..viewmodels.navigation_viewmodel import NavigationViewModel


def create_left_drawer(nav_vm: NavigationViewModel) -> None:
    """Create left navigation drawer with patient/plan/trial grids.

    Creates a left drawer containing three AgGrid components for hierarchical
    navigation through patients, plans, and trials. Selection in one grid
    cascades to populate the next grid.

    Args:
        nav_vm: NavigationViewModel instance managing navigation state
    """
    with ui.left_drawer(fixed=True).classes("bg_primary").props('width=400') as drawer:
        drawer.show()  # Always visible in desktop mode

        with ui.column().classes("w-full h-full"):
            # Section header
            ui.label("Navigation").classes("text-h6 font-medium mb-2")

            # Patients Section (1/3 of space)
            with ui.column().classes("w-full flex-1"):
                ui.label("Patients").classes("text-subtitle1 font-medium mb-2")
                create_patients_grid(nav_vm)

            # Plans Section (1/3 of space)
            with ui.column().classes("w-full flex-1 mt-4"):
                ui.label("Plans").classes("text-subtitle1 font-medium mb-2")
                create_plans_grid(nav_vm)

            # Trials Section (1/3 of space)
            with ui.column().classes("w-full flex-1 mt-4"):
                ui.label("Trials").classes("text-subtitle1 font-medium mb-2")
                create_trials_grid(nav_vm)


@ui.refreshable
def create_patients_grid(nav_vm: NavigationViewModel):
    """Create refreshable patients AgGrid.

    Args:
        nav_vm: NavigationViewModel instance
    """
    row_data = []
    if nav_vm.patients_df is not None and not nav_vm.patients_df.empty:
        row_data = nav_vm.patients_df.to_dict("records")

    grid = ui.aggrid(
        {
            "columnDefs": [
                {"field": "id", "headerName": "ID", "width": 50},
                {"field": "mrn", "headerName": "MRN", "width": 100},
                {"field": "name", "headerName": "Name", "flex": 1},
            ],
            "rowData": row_data,
            "rowSelection": "single",
            "overlayNoRowsTemplate": "<span style='padding: 10px; color: gray;'>Open an archive to view patients</span>",
        },
        theme="balham-dark"
    ).classes("w-full h-full")

    # Register selection handler
    async def selection_changed_handler(e):
        row = await grid.get_selected_row()
        nav_vm.on_patient_selected(row)
    grid.on("selectionChanged", selection_changed_handler)

    # Auto-select first row if data exists
    if row_data:
        async def select_first_row():
            try:
                # Visually select the first row (this triggers the selection handler)
                await ui.run_javascript(f'''
                    getElement({grid.id}).api.getDisplayedRowAtIndex(0)?.setSelected(true);
                ''')
            except Exception:
                # Fallback: trigger selection logic directly
                nav_vm.on_patient_selected(row_data[0])
        ui.timer(0.1, select_first_row, once=True)


@ui.refreshable
def create_plans_grid(nav_vm: NavigationViewModel):
    """Create refreshable plans AgGrid.

    Args:
        nav_vm: NavigationViewModel instance
    """
    row_data = []
    if nav_vm.plans_df is not None and not nav_vm.plans_df.empty:
        row_data = nav_vm.plans_df.to_dict("records")

    grid = ui.aggrid(
        {
            "columnDefs": [
                {"field": "id", "headerName": "ID", "width": 50},
                {"field": "name", "headerName": "Plan Name", "flex": 1},
            ],
            "rowData": row_data,
            "rowSelection": "single",
            "colorSchemeDark": True,
            "overlayNoRowsTemplate": "<span style='padding: 10px; color: gray;'>Select a patient</span>",
        },
        theme="balham-dark"
    ).classes("w-full h-full")

    # Register selection handler
    async def selection_changed_handler(e):
        row = await grid.get_selected_row()
        nav_vm.on_plan_selected(row)
    grid.on("selectionChanged", selection_changed_handler)

    # Auto-select first row if data exists
    if row_data:
        async def select_first_row():
            try:
                # Visually select the first row (this triggers the selection handler)
                await ui.run_javascript(f'''
                    getElement({grid.id}).api.getDisplayedRowAtIndex(0)?.setSelected(true);
                ''')
            except Exception:
                # Fallback: trigger selection logic directly
                nav_vm.on_plan_selected(row_data[0])
        ui.timer(0.1, select_first_row, once=True)


@ui.refreshable
def create_trials_grid(nav_vm: NavigationViewModel):
    """Create refreshable trials AgGrid.

    Args:
        nav_vm: NavigationViewModel instance
    """
    row_data = []
    if nav_vm.trials_df is not None and not nav_vm.trials_df.empty:
        row_data = nav_vm.trials_df.to_dict("records")

    grid = ui.aggrid(
        {
            "columnDefs": [
                {"field": "id", "headerName": "ID", "width": 50},
                {"field": "name", "headerName": "Trial Name", "flex": 1},
                {"field": "beams", "headerName": "Beams", "width": 70},
            ],
            "rowData": row_data,
            "rowSelection": "single",
            "colorSchemeDark": True,
            "overlayNoRowsTemplate": "<span style='padding: 10px; color: gray;'>Select a plan</span>",
        },
        theme="balham-dark"
    ).classes("w-full h-full")

    # Register selection handler
    async def selection_changed_handler(e):
        row = await grid.get_selected_row()
        nav_vm.on_trial_selected(row)
    grid.on("selectionChanged", selection_changed_handler)

    # Auto-select first row if data exists
    if row_data:
        async def select_first_row():
            try:
                # Visually select the first row (this triggers the selection handler)
                await ui.run_javascript(f'''
                    getElement({grid.id}).api.getDisplayedRowAtIndex(0)?.setSelected(true);
                ''')
            except Exception:
                # Fallback: trigger selection logic directly
                nav_vm.on_trial_selected(row_data[0])
        ui.timer(0.1, select_first_row, once=True)


def setup_refresh_callbacks(nav_vm: NavigationViewModel) -> None:
    """Set up refresh callbacks for the navigation grids.

    This connects the ViewModel's refresh methods to the UI refreshable functions.

    Args:
        nav_vm: NavigationViewModel instance
    """
    nav_vm.refresh_patients = create_patients_grid.refresh
    nav_vm.refresh_plans = create_plans_grid.refresh
    nav_vm.refresh_trials = create_trials_grid.refresh