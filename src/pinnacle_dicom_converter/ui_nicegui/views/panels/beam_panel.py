"""Beam control panel for managing radiation beam visibility and information."""

from nicegui import ui

from ...viewmodels.overlay_viewmodel import OverlayViewModel


def create_beam_panel(overlay_vm: OverlayViewModel) -> None:
    """Create beam control panel with visibility controls.

    Provides an interactive interface for managing beam display:
    - Visibility toggles
    - Beam parameters display
    - Treatment information

    Args:
        overlay_vm: OverlayViewModel instance managing overlay state
    """
    with ui.column().classes("w-full gap-3 p-3"):
        # ui.label("Beam Controls").classes("text-h6 font-medium mb-2")

        # Info card
        with ui.card().classes("w-full bg_secondary p-3 mb-2"):
            beam_count_label = ui.label(f"Beams: {len(overlay_vm.beams)}").classes(
                "text-sm"
            )
            visible_count_label = ui.label(
                f"Visible: {len(overlay_vm.visible_beams)}"
            ).classes("text-sm text-gray-400")

        # Beam list
        create_beam_list(overlay_vm, beam_count_label, visible_count_label)


@ui.refreshable
def create_beam_list(
    overlay_vm: OverlayViewModel, beam_count_label, visible_count_label
):
    """Create refreshable beam list with controls.

    Args:
        overlay_vm: OverlayViewModel instance
        beam_count_label: Label to update with beam count
        visible_count_label: Label to update with visible count
    """
    if not overlay_vm.beams:
        with ui.card().classes("w-full bg_secondary p-4"):
            ui.label("No beams loaded").classes("text-gray-500 text-center")
        return

    # Create expandable cards for each beam
    for beam in overlay_vm.beams:
        with ui.expansion(beam.name, icon="straighten").classes(
            "w-full bg_secondary"
        ).props("dense"):
            with ui.column().classes("w-full gap-2 p-2"):
                # Visibility toggle
                with ui.row().classes("w-full items-center gap-2"):
                    ui.label("Visible:").classes("text-sm")
                    visibility_switch = ui.switch(value=beam.visible).props("dense")

                    def make_toggle_handler(beam_id):
                        def handler():
                            overlay_vm.toggle_beam_visibility(beam_id)
                            # Update count labels
                            beam_count_label.text = f"Beams: {len(overlay_vm.beams)}"
                            visible_count_label.text = (
                                f"Visible: {len(overlay_vm.visible_beams)}"
                            )
                            create_beam_list.refresh()

                        return handler

                    visibility_switch.on("update:model-value", make_toggle_handler(beam.beam_id))

                # Beam information
                with ui.column().classes("w-full gap-1 mt-2"):
                    ui.label(f"ID: {beam.beam_id}").classes("text-xs text-gray-400")
                    ui.label(f"Modality: {beam.modality}").classes(
                        "text-xs text-gray-400"
                    )

                    if beam.energy:
                        ui.label(f"Energy: {beam.energy}").classes(
                            "text-xs text-gray-400"
                        )

                    if beam.gantry_angle is not None:
                        ui.label(f"Gantry: {beam.gantry_angle:.1f}°").classes(
                            "text-xs text-gray-400"
                        )

                    if beam.collimator_angle is not None:
                        ui.label(f"Collimator: {beam.collimator_angle:.1f}°").classes(
                            "text-xs text-gray-400"
                        )

                    if beam.couch_angle is not None:
                        ui.label(f"Couch: {beam.couch_angle:.1f}°").classes(
                            "text-xs text-gray-400"
                        )


def setup_refresh_callback(overlay_vm: OverlayViewModel) -> None:
    """Set up refresh callback for the beam panel.

    Args:
        overlay_vm: OverlayViewModel instance
    """
    overlay_vm.refresh_beams = create_beam_list.refresh
