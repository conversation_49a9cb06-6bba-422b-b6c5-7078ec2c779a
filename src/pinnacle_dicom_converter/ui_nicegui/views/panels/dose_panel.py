"""Dose control panel for managing dose display and isodose lines."""

from nicegui import ui

from ...viewmodels.overlay_viewmodel import OverlayViewModel


def create_dose_panel(overlay_vm: OverlayViewModel) -> None:
    """Create dose control panel with isodose line controls.

    Provides an interactive interface for managing dose display:
    - Reference dose setting
    - Isodose line visibility toggles
    - Dose level information

    Args:
        overlay_vm: OverlayViewModel instance managing overlay state
    """
    with ui.column().classes("w-full gap-3 p-3"):
        # ui.label("Dose Controls").classes("text-h6 font-medium mb-2")

        # Reference dose card
        with ui.card().classes("w-full bg_secondary p-3"):
            ui.label("Reference Dose").classes("text-subtitle2 font-medium mb-2")

            with ui.row().classes("w-full items-center gap-2"):
                dose_input = ui.number(
                    label="Dose (cGy)",
                    value=overlay_vm.dose_model.reference_dose_cgy,
                    min=0,
                    step=100,
                ).classes("flex-1").props("dense outlined")

                def on_dose_change():
                    overlay_vm.set_reference_dose(dose_input.value)

                dose_input.on("update:model-value", on_dose_change)

        # Isodose lines info
        with ui.card().classes("w-full bg_secondary p-3 mb-2"):
            isodose_count_label = ui.label(
                f"Isodose Lines: {len(overlay_vm.dose_model.isodose_lines)}"
            ).classes("text-sm")
            visible_count_label = ui.label(
                f"Visible: {len(overlay_vm.visible_isodose_lines)}"
            ).classes("text-sm text-gray-400")

        # Isodose lines list
        create_isodose_list(overlay_vm, isodose_count_label, visible_count_label)


@ui.refreshable
def create_isodose_list(
    overlay_vm: OverlayViewModel, isodose_count_label, visible_count_label
):
    """Create refreshable isodose line list with controls.

    Args:
        overlay_vm: OverlayViewModel instance
        isodose_count_label: Label to update with isodose count
        visible_count_label: Label to update with visible count
    """
    if not overlay_vm.dose_model.isodose_lines:
        with ui.card().classes("w-full bg_secondary p-4"):
            ui.label("No dose data loaded").classes("text-gray-500 text-center")
        return

    # Create cards for each isodose level
    for isodose in overlay_vm.dose_model.isodose_lines:
        with ui.card().classes("w-full bg_secondary p-3"):
            with ui.row().classes("w-full items-center gap-2"):
                # Color indicator
                ui.html(
                    f'<div style="width: 24px; height: 24px; background-color: {isodose.color}; border-radius: 4px; border: 1px solid #444;"></div>'
                )

                # Level label
                ui.label(isodose.label).classes("text-sm font-medium flex-1")

                # Visibility toggle
                visibility_switch = ui.switch(value=isodose.visible).props("dense")

                def make_toggle_handler(level):
                    def handler():
                        overlay_vm.toggle_isodose_visibility(level)
                        # Update count labels
                        isodose_count_label.text = (
                            f"Isodose Lines: {len(overlay_vm.dose_model.isodose_lines)}"
                        )
                        visible_count_label.text = (
                            f"Visible: {len(overlay_vm.visible_isodose_lines)}"
                        )
                        create_isodose_list.refresh()

                    return handler

                visibility_switch.on("update:model-value", make_toggle_handler(isodose.level))

            # Dose value in cGy
            dose_value = (
                isodose.level / 100.0 * overlay_vm.dose_model.reference_dose_cgy
            )
            ui.label(f"{dose_value:.0f} cGy").classes("text-xs text-gray-400 mt-1")


def setup_refresh_callback(overlay_vm: OverlayViewModel) -> None:
    """Set up refresh callback for the dose panel.

    Args:
        overlay_vm: OverlayViewModel instance
    """
    overlay_vm.refresh_dose = create_isodose_list.refresh
