"""POI control panel for managing point of interest visibility and appearance."""

from nicegui import ui

from ...viewmodels.overlay_viewmodel import OverlayViewModel


def create_poi_panel(overlay_vm: OverlayViewModel) -> None:
    """Create POI control panel with visibility and color controls.

    Provides an interactive interface for managing POI display properties:
    - Visibility toggles
    - Color selection
    - Marker style options
    - POI information

    Args:
        overlay_vm: OverlayViewModel instance managing overlay state
    """
    with ui.column().classes("w-full gap-3 p-3"):
        # ui.label("POI Controls").classes("text-h6 font-medium mb-2")

        # Info card
        with ui.card().classes("w-full bg_secondary p-3 mb-2"):
            poi_count_label = ui.label(f"POIs: {len(overlay_vm.pois)}").classes(
                "text-sm"
            )
            visible_count_label = ui.label(
                f"Visible: {len(overlay_vm.visible_pois)}"
            ).classes("text-sm text-gray-400")

        # POI list
        create_poi_list(overlay_vm, poi_count_label, visible_count_label)


@ui.refreshable
def create_poi_list(overlay_vm: OverlayViewModel, poi_count_label, visible_count_label):
    """Create refreshable POI list with controls.

    Args:
        overlay_vm: OverlayViewModel instance
        poi_count_label: Label to update with POI count
        visible_count_label: Label to update with visible count
    """
    if not overlay_vm.pois:
        with ui.card().classes("w-full bg_secondary p-4"):
            ui.label("No POIs loaded").classes("text-gray-500 text-center")
        return

    # Create expandable cards for each POI
    for poi in overlay_vm.pois:
        with ui.expansion(poi.name, icon="place").classes(
            "w-full bg_secondary"
        ).props("dense"):
            with ui.column().classes("w-full gap-2 p-2"):
                # Visibility toggle
                with ui.row().classes("w-full items-center gap-2"):
                    ui.label("Visible:").classes("text-sm")
                    visibility_switch = ui.switch(value=poi.visible).props("dense")

                    def make_toggle_handler(poi_id):
                        def handler():
                            overlay_vm.toggle_poi_visibility(poi_id)
                            # Update count labels
                            poi_count_label.text = f"POIs: {len(overlay_vm.pois)}"
                            visible_count_label.text = (
                                f"Visible: {len(overlay_vm.visible_pois)}"
                            )
                            create_poi_list.refresh()

                        return handler

                    visibility_switch.on("update:model-value", make_toggle_handler(poi.poi_id))

                # Color picker
                with ui.row().classes("w-full items-center gap-2"):
                    ui.label("Color:").classes("text-sm")
                    color_input = ui.color_input(
                        label="", value=poi.color
                    ).classes("flex-1").props("dense")

                    def make_color_handler(poi_id):
                        def handler(e):
                            overlay_vm.change_poi_color(poi_id, e.value)
                            create_poi_list.refresh()

                        return handler

                    color_input.on("update:model-value", make_color_handler(poi.poi_id))

                # Marker style selector
                with ui.row().classes("w-full items-center gap-2"):
                    ui.label("Marker:").classes("text-sm")
                    ui.select(
                        ["circle", "cross", "diamond", "square"],
                        value=poi.marker_style,
                    ).classes("flex-1").props("dense outlined")

                # POI info
                ui.label(f"ID: {poi.poi_id}").classes("text-xs text-gray-400")
                if poi.coordinates:
                    coord_text = f"({poi.coordinates[0]:.1f}, {poi.coordinates[1]:.1f}, {poi.coordinates[2]:.1f})"
                    ui.label(coord_text).classes("text-xs text-gray-400")


def setup_refresh_callback(overlay_vm: OverlayViewModel) -> None:
    """Set up refresh callback for the POI panel.

    Args:
        overlay_vm: OverlayViewModel instance
    """
    overlay_vm.refresh_pois = create_poi_list.refresh
