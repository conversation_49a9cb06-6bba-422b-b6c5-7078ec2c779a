"""POI to SVG converter for rendering Points of Interest as SVG overlays."""

from typing import List, Tuple, Optional
import logging
import math

from .svg_converter import SVGConverter
from ..models.poi_display import POIDisplayModel

logger = logging.getLogger(__name__)


class POISVGConverter(SVGConverter):
    """Converter for Points of Interest to SVG overlays.
    
    This class converts POI data to SVG marker elements for display
    on CT images. It supports different marker styles and sizes.
    """

    def convert_pois_to_svg(self, pois: List[POIDisplayModel]) -> str:
        """Convert list of POI display models to SVG content.
        
        Args:
            pois: List of POI display models
            
        Returns:
            Complete SVG content string for POI overlays
        """
        if not pois:
            return ""
        
        try:
            width, height = self.get_image_bounds()
            svg_parts = [self.create_svg_header(width, height)]
            
            # Create POI group
            svg_parts.append(self.create_svg_group("pois", visible=True))
            
            for poi in pois:
                if poi.visible and poi.coordinates:
                    poi_svg = self._convert_single_poi(poi)
                    if poi_svg:
                        svg_parts.append(poi_svg)
            
            svg_parts.append(self.close_svg_group())
            svg_parts.append(self.create_svg_footer())
            
            return '\n'.join(svg_parts)
            
        except Exception as e:
            logger.error(f"Failed to convert POIs to SVG: {e}")
            return ""

    def _convert_single_poi(self, poi: POIDisplayModel) -> str:
        """Convert a single POI to SVG elements.
        
        Args:
            poi: POI display model
            
        Returns:
            SVG content for this POI
        """
        try:
            if not poi.coordinates:
                return ""
            
            # Check if POI is on current slice
            if not self.is_point_on_current_slice(poi.coordinates, tolerance=2.0):
                return ""
            
            svg_parts = []
            
            # Create group for this POI
            svg_parts.append(self.create_svg_group(f"poi_{poi.poi_id}", poi.visible))
            
            # Convert to pixel coordinates
            pixel_x, pixel_y = self.world_to_pixel(poi.coordinates)
            
            # Create marker based on style
            marker_svg = self._create_poi_marker(
                position=(pixel_x, pixel_y),
                style=poi.marker_style,
                size=poi.marker_size,
                color=poi.color
            )
            svg_parts.append(marker_svg)
            
            # Add POI label
            label_svg = self._create_poi_label(poi, (pixel_x, pixel_y))
            if label_svg:
                svg_parts.append(label_svg)
            
            svg_parts.append(self.close_svg_group())
            
            return '\n'.join(svg_parts)
            
        except Exception as e:
            logger.error(f"Failed to convert POI {poi.poi_id} to SVG: {e}")
            return ""

    def _create_poi_marker(self, position: Tuple[float, float], 
                          style: str = "circle",
                          size: int = 8,
                          color: str = "#00FF00") -> str:
        """Create SVG marker for POI based on style.
        
        Args:
            position: (x, y) pixel coordinates
            style: Marker style ('circle', 'cross', 'diamond', 'square')
            size: Marker size in pixels
            color: Marker color (hex format)
            
        Returns:
            SVG element string for the marker
        """
        x, y = position
        half_size = size / 2
        
        if style == "circle":
            return self.create_svg_circle(
                center=(x, y),
                radius=half_size,
                color=color,
                fill=color,
                stroke_width=1.0
            )
        
        elif style == "cross":
            # Create cross using two lines
            line1 = f'<line x1="{x - half_size}" y1="{y}" x2="{x + half_size}" y2="{y}" stroke="{color}" stroke-width="2"/>'
            line2 = f'<line x1="{x}" y1="{y - half_size}" x2="{x}" y2="{y + half_size}" stroke="{color}" stroke-width="2"/>'
            return line1 + '\n' + line2
        
        elif style == "diamond":
            # Create diamond using path
            points = [
                (x, y - half_size),  # Top
                (x + half_size, y),  # Right
                (x, y + half_size),  # Bottom
                (x - half_size, y)   # Left
            ]
            return self.create_svg_path(
                points=points,
                color=color,
                stroke_width=2.0,
                fill=color,
                closed=True
            )
        
        elif style == "square":
            # Create square using rect
            return f'<rect x="{x - half_size}" y="{y - half_size}" width="{size}" height="{size}" stroke="{color}" stroke-width="2" fill="{color}"/>'
        
        else:
            # Default to circle
            return self.create_svg_circle(
                center=(x, y),
                radius=half_size,
                color=color,
                fill=color,
                stroke_width=1.0
            )

    def _create_poi_label(self, poi: POIDisplayModel, 
                         position: Tuple[float, float]) -> str:
        """Create SVG text label for POI.
        
        Args:
            poi: POI display model
            position: (x, y) pixel coordinates of POI marker
            
        Returns:
            SVG text element for POI label
        """
        try:
            x, y = position
            
            # Offset label from marker
            label_x = x + poi.marker_size + 2
            label_y = y - poi.marker_size // 2
            
            return self.create_svg_text(
                position=(label_x, label_y),
                text=poi.name,
                color=poi.color,
                font_size=10
            )
            
        except Exception as e:
            logger.error(f"Failed to create label for POI {poi.poi_id}: {e}")
            return ""

    def get_poi_at_point(self, pois: List[POIDisplayModel], 
                        pixel_coords: Tuple[float, float],
                        tolerance: float = 10.0) -> Optional[POIDisplayModel]:
        """Find POI near the given pixel coordinates.
        
        Args:
            pois: List of POI display models
            pixel_coords: (x, y) pixel coordinates
            tolerance: Distance tolerance in pixels
            
        Returns:
            POI near the point, or None
        """
        try:
            click_x, click_y = pixel_coords
            
            for poi in pois:
                if not poi.visible or not poi.coordinates:
                    continue
                
                # Check if POI is on current slice
                if not self.is_point_on_current_slice(poi.coordinates, tolerance=2.0):
                    continue
                
                # Convert POI to pixel coordinates
                poi_x, poi_y = self.world_to_pixel(poi.coordinates)
                
                # Calculate distance
                distance = math.sqrt((click_x - poi_x)**2 + (click_y - poi_y)**2)
                
                if distance <= tolerance:
                    return poi
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to find POI at point: {e}")
            return None

    def create_poi_crosshair(self, position: Tuple[float, float],
                           size: int = 20,
                           color: str = "#FFFF00") -> str:
        """Create crosshair marker for POI selection/highlighting.
        
        Args:
            position: (x, y) pixel coordinates
            size: Crosshair size in pixels
            color: Crosshair color (hex format)
            
        Returns:
            SVG crosshair element
        """
        x, y = position
        half_size = size / 2
        
        # Create crosshair with circle
        circle = self.create_svg_circle(
            center=(x, y),
            radius=half_size,
            color=color,
            fill="none",
            stroke_width=2.0
        )
        
        # Add cross lines
        line1 = f'<line x1="{x - half_size}" y1="{y}" x2="{x + half_size}" y2="{y}" stroke="{color}" stroke-width="1"/>'
        line2 = f'<line x1="{x}" y1="{y - half_size}" x2="{x}" y2="{y + half_size}" stroke="{color}" stroke-width="1"/>'
        
        return circle + '\n' + line1 + '\n' + line2

    def get_poi_info_text(self, poi: POIDisplayModel) -> str:
        """Get formatted info text for POI.
        
        Args:
            poi: POI display model
            
        Returns:
            Formatted info string
        """
        if not poi.coordinates:
            return f"{poi.name}: No coordinates"
        
        x, y, z = poi.coordinates
        return f"{poi.name}: ({x:.1f}, {y:.1f}, {z:.1f}) mm"
