"""Base SVG converter for converting geometry to SVG overlays."""

from typing import List, Tuple, Optional
import numpy as np
import logging

logger = logging.getLogger(__name__)


class SVGConverter:
    """Base class for converting geometry to SVG overlays.
    
    This class handles coordinate transformations from world coordinates (mm)
    to pixel coordinates for SVG rendering on CT images.
    """

    def __init__(self, pixel_spacing: <PERSON><PERSON>[float, float, float], 
                 image_origin: Tuple[float, float, float],
                 image_dimensions: Tuple[int, int, int], 
                 zoom_factor: float = 1.0,
                 current_slice: int = 0,
                 orientation: str = "axial"):
        """Initialize SVG converter with image parameters.
        
        Args:
            pixel_spacing: (x, y, z) pixel spacing in mm
            image_origin: (x, y, z) image origin in mm
            image_dimensions: (width, height, depth) in pixels
            zoom_factor: Current zoom level (1.0 = 100%)
            current_slice: Current slice index
            orientation: Current view orientation ('axial', 'sagittal', 'coronal')
        """
        self.pixel_spacing = pixel_spacing
        self.image_origin = image_origin
        self.image_dimensions = image_dimensions
        self.zoom_factor = zoom_factor
        self.current_slice = current_slice
        self.orientation = orientation.lower()

    def world_to_pixel(self, world_coords: Tuple[float, float, float]) -> Tuple[float, float]:
        """Convert world coordinates (mm) to pixel coordinates.
        
        Args:
            world_coords: (x, y, z) coordinates in mm
            
        Returns:
            (pixel_x, pixel_y) coordinates for SVG
        """
        x, y, z = world_coords

        if self.orientation == "axial":
            # Axial view: X-Y plane, Z is slice
            pixel_x = (x - self.image_origin[0]) / self.pixel_spacing[0]
            pixel_y = (y - self.image_origin[1]) / self.pixel_spacing[1]
        elif self.orientation == "sagittal":
            # Sagittal view: Y-Z plane, X is slice
            pixel_x = (y - self.image_origin[1]) / self.pixel_spacing[1]
            pixel_y = (z - self.image_origin[2]) / self.pixel_spacing[2]
        elif self.orientation == "coronal":
            # Coronal view: X-Z plane, Y is slice
            pixel_x = (x - self.image_origin[0]) / self.pixel_spacing[0]
            pixel_y = (z - self.image_origin[2]) / self.pixel_spacing[2]
        else:
            # Default to axial
            pixel_x = (x - self.image_origin[0]) / self.pixel_spacing[0]
            pixel_y = (y - self.image_origin[1]) / self.pixel_spacing[1]

        # Apply zoom factor
        pixel_x *= self.zoom_factor
        pixel_y *= self.zoom_factor

        return (pixel_x, pixel_y)

    def is_point_on_current_slice(self, world_coords: Tuple[float, float, float], 
                                  tolerance: float = 2.0) -> bool:
        """Check if a 3D point is on or near the current slice.
        
        Args:
            world_coords: (x, y, z) coordinates in mm
            tolerance: Distance tolerance in mm
            
        Returns:
            True if point is on current slice within tolerance
        """
        x, y, z = world_coords
        
        if self.orientation == "axial":
            # Check Z coordinate
            slice_z = self.image_origin[2] + (self.current_slice * self.pixel_spacing[2])
            return abs(z - slice_z) <= tolerance
        elif self.orientation == "sagittal":
            # Check X coordinate
            slice_x = self.image_origin[0] + (self.current_slice * self.pixel_spacing[0])
            return abs(x - slice_x) <= tolerance
        elif self.orientation == "coronal":
            # Check Y coordinate
            slice_y = self.image_origin[1] + (self.current_slice * self.pixel_spacing[1])
            return abs(y - slice_y) <= tolerance
        
        return False

    def create_svg_header(self, width: int, height: int) -> str:
        """Create SVG header with viewBox.
        
        Args:
            width: SVG width in pixels
            height: SVG height in pixels
            
        Returns:
            SVG header string
        """
        return f'<svg width="{width}" height="{height}" viewBox="0 0 {width} {height}" xmlns="http://www.w3.org/2000/svg">'

    def create_svg_footer(self) -> str:
        """Create SVG footer.
        
        Returns:
            SVG footer string
        """
        return '</svg>'

    def create_svg_group(self, group_id: str, visible: bool = True, opacity: float = 1.0) -> str:
        """Create SVG group element.
        
        Args:
            group_id: Unique identifier for the group
            visible: Whether the group should be visible
            opacity: Group opacity (0.0 to 1.0)
            
        Returns:
            SVG group opening tag
        """
        visibility = 'visible' if visible else 'hidden'
        return f'<g id="{group_id}" visibility="{visibility}" opacity="{opacity}">'

    def close_svg_group(self) -> str:
        """Close SVG group.
        
        Returns:
            SVG group closing tag
        """
        return '</g>'

    def create_svg_path(self, points: List[Tuple[float, float]], 
                       color: str = "#FF0000", 
                       stroke_width: float = 2.0,
                       fill: str = "none",
                       closed: bool = True) -> str:
        """Create SVG path element from points.
        
        Args:
            points: List of (x, y) pixel coordinates
            color: Stroke color (hex format)
            stroke_width: Line width in pixels
            fill: Fill color or "none"
            closed: Whether to close the path
            
        Returns:
            SVG path element string
        """
        if not points:
            return ""
        
        # Start path
        path_data = f"M {points[0][0]:.2f} {points[0][1]:.2f}"
        
        # Add line segments
        for point in points[1:]:
            path_data += f" L {point[0]:.2f} {point[1]:.2f}"
        
        # Close path if requested
        if closed:
            path_data += " Z"
        
        return f'<path d="{path_data}" stroke="{color}" stroke-width="{stroke_width}" fill="{fill}"/>'

    def create_svg_circle(self, center: Tuple[float, float], 
                         radius: float = 3.0,
                         color: str = "#00FF00",
                         fill: str = "none",
                         stroke_width: float = 2.0) -> str:
        """Create SVG circle element.
        
        Args:
            center: (x, y) pixel coordinates of center
            radius: Circle radius in pixels
            color: Stroke color (hex format)
            fill: Fill color or "none"
            stroke_width: Line width in pixels
            
        Returns:
            SVG circle element string
        """
        cx, cy = center
        return f'<circle cx="{cx:.2f}" cy="{cy:.2f}" r="{radius}" stroke="{color}" stroke-width="{stroke_width}" fill="{fill}"/>'

    def create_svg_text(self, position: Tuple[float, float], 
                       text: str,
                       color: str = "#FFFFFF",
                       font_size: int = 12,
                       font_family: str = "Arial") -> str:
        """Create SVG text element.
        
        Args:
            position: (x, y) pixel coordinates
            text: Text content
            color: Text color (hex format)
            font_size: Font size in pixels
            font_family: Font family name
            
        Returns:
            SVG text element string
        """
        x, y = position
        return f'<text x="{x:.2f}" y="{y:.2f}" fill="{color}" font-size="{font_size}" font-family="{font_family}">{text}</text>'

    def get_image_bounds(self) -> Tuple[int, int]:
        """Get current image bounds in pixels.
        
        Returns:
            (width, height) of current view
        """
        if self.orientation == "axial":
            return (self.image_dimensions[0], self.image_dimensions[1])
        elif self.orientation == "sagittal":
            return (self.image_dimensions[1], self.image_dimensions[2])
        elif self.orientation == "coronal":
            return (self.image_dimensions[0], self.image_dimensions[2])
        else:
            return (self.image_dimensions[0], self.image_dimensions[1])
