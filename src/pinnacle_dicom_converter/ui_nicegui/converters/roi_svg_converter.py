"""ROI to SVG converter for rendering ROI contours as SVG overlays."""

from typing import List, Tuple, Optional
import logging

from .svg_converter import SVGConverter
from ..models.roi_display import ROIDisplayModel

logger = logging.getLogger(__name__)


class ROISVGConverter(SVGConverter):
    """Converter for ROI contours to SVG overlays.
    
    This class converts ROI contour data to SVG path elements for display
    on CT images. It handles contour interpolation and slice filtering.
    """

    def convert_rois_to_svg(self, rois: List[ROIDisplayModel]) -> str:
        """Convert list of ROI display models to SVG content.
        
        Args:
            rois: List of ROI display models with contour data
            
        Returns:
            Complete SVG content string for ROI overlays
        """
        if not rois:
            return ""
        
        try:
            width, height = self.get_image_bounds()
            svg_parts = [self.create_svg_header(width, height)]
            
            # Create ROI group
            svg_parts.append(self.create_svg_group("rois", visible=True))
            
            for roi in rois:
                if roi.visible and roi.contours:
                    roi_svg = self._convert_single_roi(roi)
                    if roi_svg:
                        svg_parts.append(roi_svg)
            
            svg_parts.append(self.close_svg_group())
            svg_parts.append(self.create_svg_footer())
            
            return '\n'.join(svg_parts)
            
        except Exception as e:
            logger.error(f"Failed to convert ROIs to SVG: {e}")
            return ""

    def _convert_single_roi(self, roi: ROIDisplayModel) -> str:
        """Convert a single ROI to SVG elements.
        
        Args:
            roi: ROI display model with contour data
            
        Returns:
            SVG content for this ROI
        """
        try:
            svg_parts = []
            
            # Create group for this ROI
            svg_parts.append(self.create_svg_group(f"roi_{roi.roi_id}", roi.visible))
            
            # Process each contour
            for contour_idx, contour in enumerate(roi.contours):
                if not contour:
                    continue
                
                # Filter contour points for current slice
                slice_points = self._filter_contour_for_slice(contour)
                
                if len(slice_points) >= 3:  # Need at least 3 points for a contour
                    # Convert to pixel coordinates
                    pixel_points = []
                    for point in slice_points:
                        pixel_x, pixel_y = self.world_to_pixel(point)
                        pixel_points.append((pixel_x, pixel_y))
                    
                    # Create SVG path
                    path_svg = self.create_svg_path(
                        points=pixel_points,
                        color=roi.color,
                        stroke_width=2.0,
                        fill="none",
                        closed=True
                    )
                    svg_parts.append(path_svg)
            
            # Add ROI label if there are visible contours
            if len(svg_parts) > 1:  # More than just the group opening tag
                label_svg = self._create_roi_label(roi)
                if label_svg:
                    svg_parts.append(label_svg)
            
            svg_parts.append(self.close_svg_group())
            
            return '\n'.join(svg_parts) if len(svg_parts) > 2 else ""
            
        except Exception as e:
            logger.error(f"Failed to convert ROI {roi.roi_id} to SVG: {e}")
            return ""

    def _filter_contour_for_slice(self, contour: List[Tuple[float, float, float]], 
                                 tolerance: float = 1.0) -> List[Tuple[float, float, float]]:
        """Filter contour points that are on or near the current slice.
        
        Args:
            contour: List of (x, y, z) world coordinates
            tolerance: Distance tolerance in mm
            
        Returns:
            List of points on current slice
        """
        slice_points = []
        
        for point in contour:
            if self.is_point_on_current_slice(point, tolerance):
                slice_points.append(point)
        
        return slice_points

    def _create_roi_label(self, roi: ROIDisplayModel) -> str:
        """Create SVG text label for ROI.
        
        Args:
            roi: ROI display model
            
        Returns:
            SVG text element for ROI label
        """
        try:
            # Find a good position for the label (first visible contour point)
            for contour in roi.contours:
                slice_points = self._filter_contour_for_slice(contour)
                if slice_points:
                    # Use first point of first visible contour
                    pixel_x, pixel_y = self.world_to_pixel(slice_points[0])
                    
                    # Offset label slightly from contour
                    label_x = pixel_x + 5
                    label_y = pixel_y - 5
                    
                    return self.create_svg_text(
                        position=(label_x, label_y),
                        text=roi.name,
                        color=roi.color,
                        font_size=10
                    )
            
            return ""
            
        except Exception as e:
            logger.error(f"Failed to create label for ROI {roi.roi_id}: {e}")
            return ""

    def get_roi_at_point(self, rois: List[ROIDisplayModel], 
                        pixel_coords: Tuple[float, float]) -> Optional[ROIDisplayModel]:
        """Find ROI that contains the given pixel coordinates.
        
        Args:
            rois: List of ROI display models
            pixel_coords: (x, y) pixel coordinates
            
        Returns:
            ROI that contains the point, or None
        """
        try:
            # Convert pixel coordinates back to world coordinates
            world_coords = self._pixel_to_world(pixel_coords)
            
            for roi in rois:
                if not roi.visible or not roi.contours:
                    continue
                
                # Check if point is inside any contour of this ROI
                for contour in roi.contours:
                    slice_points = self._filter_contour_for_slice(contour)
                    if len(slice_points) >= 3:
                        if self._point_in_polygon(world_coords[:2], slice_points):
                            return roi
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to find ROI at point: {e}")
            return None

    def _pixel_to_world(self, pixel_coords: Tuple[float, float]) -> Tuple[float, float, float]:
        """Convert pixel coordinates back to world coordinates.
        
        Args:
            pixel_coords: (x, y) pixel coordinates
            
        Returns:
            (x, y, z) world coordinates
        """
        pixel_x, pixel_y = pixel_coords
        
        # Reverse zoom factor
        pixel_x /= self.zoom_factor
        pixel_y /= self.zoom_factor
        
        if self.orientation == "axial":
            x = self.image_origin[0] + (pixel_x * self.pixel_spacing[0])
            y = self.image_origin[1] + (pixel_y * self.pixel_spacing[1])
            z = self.image_origin[2] + (self.current_slice * self.pixel_spacing[2])
        elif self.orientation == "sagittal":
            x = self.image_origin[0] + (self.current_slice * self.pixel_spacing[0])
            y = self.image_origin[1] + (pixel_x * self.pixel_spacing[1])
            z = self.image_origin[2] + (pixel_y * self.pixel_spacing[2])
        elif self.orientation == "coronal":
            x = self.image_origin[0] + (pixel_x * self.pixel_spacing[0])
            y = self.image_origin[1] + (self.current_slice * self.pixel_spacing[1])
            z = self.image_origin[2] + (pixel_y * self.pixel_spacing[2])
        else:
            x = self.image_origin[0] + (pixel_x * self.pixel_spacing[0])
            y = self.image_origin[1] + (pixel_y * self.pixel_spacing[1])
            z = self.image_origin[2] + (self.current_slice * self.pixel_spacing[2])
        
        return (x, y, z)

    def _point_in_polygon(self, point: Tuple[float, float], 
                         polygon: List[Tuple[float, float, float]]) -> bool:
        """Check if a 2D point is inside a polygon using ray casting.
        
        Args:
            point: (x, y) coordinates to test
            polygon: List of (x, y, z) polygon vertices (z ignored)
            
        Returns:
            True if point is inside polygon
        """
        if len(polygon) < 3:
            return False
        
        x, y = point
        n = len(polygon)
        inside = False
        
        p1x, p1y = polygon[0][0], polygon[0][1]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n][0], polygon[i % n][1]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
