"""Beam to SVG converter for rendering beam geometry as SVG overlays."""

from typing import List, Tuple, Optional
import logging
import math

from .svg_converter import SVGConverter
from ..models.beam_display import BeamDisplayModel

logger = logging.getLogger(__name__)


class BeamSVGConverter(SVGConverter):
    """Converter for beam geometry to SVG overlays.
    
    This class converts beam data to SVG elements for display
    on CT images, showing beam central axis and field edges.
    """

    def convert_beams_to_svg(self, beams: List[BeamDisplayModel]) -> str:
        """Convert list of beam display models to SVG content.
        
        Args:
            beams: List of beam display models
            
        Returns:
            Complete SVG content string for beam overlays
        """
        if not beams:
            return ""
        
        try:
            width, height = self.get_image_bounds()
            svg_parts = [self.create_svg_header(width, height)]
            
            # Create beam group
            svg_parts.append(self.create_svg_group("beams", visible=True))
            
            for beam in beams:
                if beam.visible:
                    beam_svg = self._convert_single_beam(beam)
                    if beam_svg:
                        svg_parts.append(beam_svg)
            
            svg_parts.append(self.close_svg_group())
            svg_parts.append(self.create_svg_footer())
            
            return '\n'.join(svg_parts)
            
        except Exception as e:
            logger.error(f"Failed to convert beams to SVG: {e}")
            return ""

    def _convert_single_beam(self, beam: BeamDisplayModel) -> str:
        """Convert a single beam to SVG elements.
        
        Args:
            beam: Beam display model
            
        Returns:
            SVG content for this beam
        """
        try:
            svg_parts = []
            
            # Create group for this beam
            svg_parts.append(self.create_svg_group(f"beam_{beam.beam_id}", beam.visible))
            
            # Calculate beam geometry based on gantry angle
            beam_geometry = self._calculate_beam_geometry(beam)
            
            if beam_geometry:
                # Draw central axis
                central_axis_svg = self._create_central_axis(beam_geometry, beam)
                if central_axis_svg:
                    svg_parts.append(central_axis_svg)
                
                # Draw field edges (simplified representation)
                field_edges_svg = self._create_field_edges(beam_geometry, beam)
                if field_edges_svg:
                    svg_parts.append(field_edges_svg)
                
                # Add beam label
                label_svg = self._create_beam_label(beam, beam_geometry)
                if label_svg:
                    svg_parts.append(label_svg)
            
            svg_parts.append(self.close_svg_group())
            
            return '\n'.join(svg_parts) if len(svg_parts) > 2 else ""
            
        except Exception as e:
            logger.error(f"Failed to convert beam {beam.beam_id} to SVG: {e}")
            return ""

    def _calculate_beam_geometry(self, beam: BeamDisplayModel) -> Optional[dict]:
        """Calculate beam geometry for current slice and orientation.
        
        Args:
            beam: Beam display model
            
        Returns:
            Dictionary with beam geometry data, or None if not applicable
        """
        try:
            # For simplified implementation, assume isocenter at image center
            width, height = self.get_image_bounds()
            isocenter_pixel = (width / 2, height / 2)
            
            # Convert gantry angle to radians
            gantry_rad = math.radians(beam.gantry_angle)
            
            # Calculate beam direction based on orientation and gantry angle
            if self.orientation == "axial":
                # In axial view, gantry rotation affects X-Y plane
                beam_dx = math.cos(gantry_rad)
                beam_dy = math.sin(gantry_rad)
            elif self.orientation == "sagittal":
                # In sagittal view, show beam if it intersects this plane
                # Simplified: show as vertical line if gantry is 0° or 180°
                if abs(beam.gantry_angle) < 30 or abs(beam.gantry_angle - 180) < 30:
                    beam_dx = 0
                    beam_dy = 1
                else:
                    return None  # Beam doesn't intersect this sagittal plane significantly
            elif self.orientation == "coronal":
                # In coronal view, show beam if it intersects this plane
                # Simplified: show as horizontal line if gantry is 90° or 270°
                if abs(beam.gantry_angle - 90) < 30 or abs(beam.gantry_angle - 270) < 30:
                    beam_dx = 1
                    beam_dy = 0
                else:
                    return None  # Beam doesn't intersect this coronal plane significantly
            else:
                return None
            
            # Calculate beam line endpoints (extend across image)
            beam_length = max(width, height)
            start_x = isocenter_pixel[0] - beam_dx * beam_length
            start_y = isocenter_pixel[1] - beam_dy * beam_length
            end_x = isocenter_pixel[0] + beam_dx * beam_length
            end_y = isocenter_pixel[1] + beam_dy * beam_length
            
            return {
                'isocenter': isocenter_pixel,
                'start_point': (start_x, start_y),
                'end_point': (end_x, end_y),
                'direction': (beam_dx, beam_dy),
                'gantry_angle': beam.gantry_angle
            }
            
        except Exception as e:
            logger.error(f"Failed to calculate beam geometry: {e}")
            return None

    def _create_central_axis(self, geometry: dict, beam: BeamDisplayModel) -> str:
        """Create SVG line for beam central axis.
        
        Args:
            geometry: Beam geometry dictionary
            beam: Beam display model
            
        Returns:
            SVG line element for central axis
        """
        try:
            start_point = geometry['start_point']
            end_point = geometry['end_point']
            
            # Create dashed line for central axis
            x1, y1 = start_point
            x2, y2 = end_point
            
            return f'<line x1="{x1:.2f}" y1="{y1:.2f}" x2="{x2:.2f}" y2="{y2:.2f}" stroke="#00FFFF" stroke-width="2" stroke-dasharray="10,5"/>'
            
        except Exception as e:
            logger.error(f"Failed to create central axis: {e}")
            return ""

    def _create_field_edges(self, geometry: dict, beam: BeamDisplayModel) -> str:
        """Create SVG lines for beam field edges.
        
        Args:
            geometry: Beam geometry dictionary
            beam: Beam display model
            
        Returns:
            SVG elements for field edges
        """
        try:
            # Simplified field representation - create parallel lines
            # representing approximate field edges
            
            isocenter = geometry['isocenter']
            direction = geometry['direction']
            
            # Assume 10cm x 10cm field at isocenter (simplified)
            field_width_pixels = 50  # Approximate field width in pixels
            
            # Calculate perpendicular direction for field edges
            perp_dx = -direction[1]
            perp_dy = direction[0]
            
            # Calculate field edge points
            edge1_start = (
                isocenter[0] + perp_dx * field_width_pixels - direction[0] * 100,
                isocenter[1] + perp_dy * field_width_pixels - direction[1] * 100
            )
            edge1_end = (
                isocenter[0] + perp_dx * field_width_pixels + direction[0] * 100,
                isocenter[1] + perp_dy * field_width_pixels + direction[1] * 100
            )
            
            edge2_start = (
                isocenter[0] - perp_dx * field_width_pixels - direction[0] * 100,
                isocenter[1] - perp_dy * field_width_pixels - direction[1] * 100
            )
            edge2_end = (
                isocenter[0] - perp_dx * field_width_pixels + direction[0] * 100,
                isocenter[1] - perp_dy * field_width_pixels + direction[1] * 100
            )
            
            # Create field edge lines
            edge1 = f'<line x1="{edge1_start[0]:.2f}" y1="{edge1_start[1]:.2f}" x2="{edge1_end[0]:.2f}" y2="{edge1_end[1]:.2f}" stroke="#FFFF00" stroke-width="1" stroke-dasharray="5,3"/>'
            edge2 = f'<line x1="{edge2_start[0]:.2f}" y1="{edge2_start[1]:.2f}" x2="{edge2_end[0]:.2f}" y2="{edge2_end[1]:.2f}" stroke="#FFFF00" stroke-width="1" stroke-dasharray="5,3"/>'
            
            return edge1 + '\n' + edge2
            
        except Exception as e:
            logger.error(f"Failed to create field edges: {e}")
            return ""

    def _create_beam_label(self, beam: BeamDisplayModel, geometry: dict) -> str:
        """Create SVG text label for beam.
        
        Args:
            beam: Beam display model
            geometry: Beam geometry dictionary
            
        Returns:
            SVG text element for beam label
        """
        try:
            isocenter = geometry['isocenter']
            
            # Position label near isocenter
            label_x = isocenter[0] + 10
            label_y = isocenter[1] - 10
            
            # Create label text with beam info
            label_text = f"{beam.name} (G{beam.gantry_angle:.0f}°)"
            
            return self.create_svg_text(
                position=(label_x, label_y),
                text=label_text,
                color="#00FFFF",
                font_size=10
            )
            
        except Exception as e:
            logger.error(f"Failed to create beam label: {e}")
            return ""

    def create_isocenter_marker(self, position: Tuple[float, float],
                               color: str = "#FF00FF") -> str:
        """Create SVG marker for isocenter.
        
        Args:
            position: (x, y) pixel coordinates
            color: Marker color (hex format)
            
        Returns:
            SVG crosshair element for isocenter
        """
        x, y = position
        size = 10
        
        # Create crosshair marker
        circle = self.create_svg_circle(
            center=(x, y),
            radius=3,
            color=color,
            fill="none",
            stroke_width=2.0
        )
        
        # Add cross lines
        line1 = f'<line x1="{x - size}" y1="{y}" x2="{x + size}" y2="{y}" stroke="{color}" stroke-width="2"/>'
        line2 = f'<line x1="{x}" y1="{y - size}" x2="{x}" y2="{y + size}" stroke="{color}" stroke-width="2"/>'
        
        return circle + '\n' + line1 + '\n' + line2

    def get_beam_info_text(self, beam: BeamDisplayModel) -> str:
        """Get formatted info text for beam.
        
        Args:
            beam: Beam display model
            
        Returns:
            Formatted info string
        """
        return f"{beam.name}: G{beam.gantry_angle:.0f}° C{beam.collimator_angle:.0f}° T{beam.couch_angle:.0f}° {beam.energy:.0f}MV"
