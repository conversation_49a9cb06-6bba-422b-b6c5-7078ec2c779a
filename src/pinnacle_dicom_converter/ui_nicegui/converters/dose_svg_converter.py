"""Dose/Isodose to SVG converter for rendering dose overlays."""

from typing import List, Tuple, Optional
import logging
import numpy as np

from .svg_converter import SVGConverter
from ..models.dose_display import DoseDisplayModel, IsodoseDisplayModel

logger = logging.getLogger(__name__)


class DoseSVGConverter(SVGConverter):
    """Converter for dose and isodose lines to SVG overlays.
    
    This class converts dose data and isodose contours to SVG elements
    for display on CT images.
    """

    def convert_dose_to_svg(self, dose_model: DoseDisplayModel) -> str:
        """Convert dose display model to SVG content.
        
        Args:
            dose_model: Dose display model with isodose lines
            
        Returns:
            Complete SVG content string for dose overlays
        """
        if not dose_model or not dose_model.isodose_lines:
            return ""
        
        try:
            width, height = self.get_image_bounds()
            svg_parts = [self.create_svg_header(width, height)]
            
            # Create dose group
            svg_parts.append(self.create_svg_group("dose", visible=True, opacity=0.8))
            
            # Convert each visible isodose line
            for isodose in dose_model.isodose_lines:
                if isodose.visible:
                    isodose_svg = self._convert_single_isodose(isodose)
                    if isodose_svg:
                        svg_parts.append(isodose_svg)
            
            svg_parts.append(self.close_svg_group())
            svg_parts.append(self.create_svg_footer())
            
            return '\n'.join(svg_parts)
            
        except Exception as e:
            logger.error(f"Failed to convert dose to SVG: {e}")
            return ""

    def _convert_single_isodose(self, isodose: IsodoseDisplayModel) -> str:
        """Convert a single isodose line to SVG elements.
        
        Args:
            isodose: Isodose display model
            
        Returns:
            SVG content for this isodose line
        """
        try:
            if not isodose.contours:
                return ""
            
            svg_parts = []
            
            # Create group for this isodose level
            svg_parts.append(self.create_svg_group(f"isodose_{isodose.level}", isodose.visible))
            
            # Process each contour
            for contour_idx, contour in enumerate(isodose.contours):
                if not contour:
                    continue
                
                # Filter contour points for current slice
                slice_points = self._filter_contour_for_slice(contour)
                
                if len(slice_points) >= 3:  # Need at least 3 points for a contour
                    # Convert to pixel coordinates
                    pixel_points = []
                    for point in slice_points:
                        pixel_x, pixel_y = self.world_to_pixel(point)
                        pixel_points.append((pixel_x, pixel_y))
                    
                    # Create SVG path for isodose line
                    path_svg = self.create_svg_path(
                        points=pixel_points,
                        color=isodose.color,
                        stroke_width=isodose.line_width,
                        fill="none",
                        closed=True
                    )
                    svg_parts.append(path_svg)
            
            # Add isodose label if there are visible contours
            if len(svg_parts) > 1:  # More than just the group opening tag
                label_svg = self._create_isodose_label(isodose)
                if label_svg:
                    svg_parts.append(label_svg)
            
            svg_parts.append(self.close_svg_group())
            
            return '\n'.join(svg_parts) if len(svg_parts) > 2 else ""
            
        except Exception as e:
            logger.error(f"Failed to convert isodose {isodose.level}% to SVG: {e}")
            return ""

    def _filter_contour_for_slice(self, contour: List[Tuple[float, float, float]], 
                                 tolerance: float = 1.0) -> List[Tuple[float, float, float]]:
        """Filter contour points that are on or near the current slice.
        
        Args:
            contour: List of (x, y, z) world coordinates
            tolerance: Distance tolerance in mm
            
        Returns:
            List of points on current slice
        """
        slice_points = []
        
        for point in contour:
            if self.is_point_on_current_slice(point, tolerance):
                slice_points.append(point)
        
        return slice_points

    def _create_isodose_label(self, isodose: IsodoseDisplayModel) -> str:
        """Create SVG text label for isodose line.
        
        Args:
            isodose: Isodose display model
            
        Returns:
            SVG text element for isodose label
        """
        try:
            # Find a good position for the label (first visible contour point)
            for contour in isodose.contours:
                slice_points = self._filter_contour_for_slice(contour)
                if slice_points:
                    # Use first point of first visible contour
                    pixel_x, pixel_y = self.world_to_pixel(slice_points[0])
                    
                    # Offset label slightly from contour
                    label_x = pixel_x + 3
                    label_y = pixel_y - 3
                    
                    # Use the label from the model, or create one
                    label_text = isodose.label or f"{isodose.level:.0f}%"
                    
                    return self.create_svg_text(
                        position=(label_x, label_y),
                        text=label_text,
                        color=isodose.color,
                        font_size=9
                    )
            
            return ""
            
        except Exception as e:
            logger.error(f"Failed to create label for isodose {isodose.level}%: {e}")
            return ""

    def create_dose_colorwash(self, dose_data: Optional[np.ndarray],
                             dose_levels: List[float],
                             colors: List[str],
                             opacity: float = 0.5) -> str:
        """Create SVG colorwash overlay for dose distribution.
        
        Args:
            dose_data: 2D dose array for current slice
            dose_levels: Dose threshold levels
            colors: Colors for each dose level
            opacity: Overall opacity for colorwash
            
        Returns:
            SVG content for dose colorwash
        """
        if dose_data is None or len(dose_levels) == 0:
            return ""
        
        try:
            svg_parts = []
            
            # Create colorwash group
            svg_parts.append(self.create_svg_group("dose_colorwash", visible=True, opacity=opacity))
            
            # For now, create a simple implementation
            # In a full implementation, this would create colored regions
            # based on dose thresholds using SVG patterns or gradients
            
            # Placeholder: create a simple gradient overlay
            width, height = self.get_image_bounds()
            gradient_def = f'''
            <defs>
                <linearGradient id="doseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:{colors[0] if colors else '#FF0000'};stop-opacity:0"/>
                    <stop offset="100%" style="stop-color:{colors[-1] if colors else '#0000FF'};stop-opacity:0.3"/>
                </linearGradient>
            </defs>
            <rect x="0" y="0" width="{width}" height="{height}" fill="url(#doseGradient)"/>
            '''
            svg_parts.append(gradient_def)
            
            svg_parts.append(self.close_svg_group())
            
            return '\n'.join(svg_parts)
            
        except Exception as e:
            logger.error(f"Failed to create dose colorwash: {e}")
            return ""

    def get_dose_value_at_point(self, dose_data: Optional[np.ndarray],
                               pixel_coords: Tuple[float, float],
                               reference_dose: float = 3000.0) -> Optional[float]:
        """Get dose value at specific pixel coordinates.
        
        Args:
            dose_data: 2D dose array for current slice
            pixel_coords: (x, y) pixel coordinates
            reference_dose: Reference dose in cGy
            
        Returns:
            Dose value in cGy, or None if not available
        """
        if dose_data is None:
            return None
        
        try:
            x, y = pixel_coords
            
            # Convert to array indices
            row = int(y)
            col = int(x)
            
            # Check bounds
            if 0 <= row < dose_data.shape[0] and 0 <= col < dose_data.shape[1]:
                # Get normalized dose value and convert to cGy
                normalized_dose = dose_data[row, col]
                dose_cgy = normalized_dose * reference_dose
                return dose_cgy
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get dose value at point: {e}")
            return None

    def create_dose_profile_line(self, start_point: Tuple[float, float],
                                end_point: Tuple[float, float],
                                color: str = "#FFFF00") -> str:
        """Create SVG line for dose profile measurement.
        
        Args:
            start_point: (x, y) pixel coordinates of start
            end_point: (x, y) pixel coordinates of end
            color: Line color (hex format)
            
        Returns:
            SVG line element
        """
        x1, y1 = start_point
        x2, y2 = end_point
        
        return f'<line x1="{x1:.2f}" y1="{y1:.2f}" x2="{x2:.2f}" y2="{y2:.2f}" stroke="{color}" stroke-width="2" stroke-dasharray="5,5"/>'

    def get_isodose_info_text(self, isodose: IsodoseDisplayModel,
                             reference_dose: float = 3000.0) -> str:
        """Get formatted info text for isodose line.
        
        Args:
            isodose: Isodose display model
            reference_dose: Reference dose in cGy
            
        Returns:
            Formatted info string
        """
        dose_cgy = (isodose.level / 100.0) * reference_dose
        return f"{isodose.level:.0f}% ({dose_cgy:.0f} cGy)"
