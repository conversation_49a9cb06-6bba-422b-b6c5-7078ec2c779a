"""Dark theme configuration for Pinnacle DICOM Converter.

This module provides dark theme styling matching the "darkly" theme aesthetic
with professional medical application appearance.
"""

from nicegui import ui


def apply_dark_theme() -> None:
    """Apply dark theme configuration to the NiceGUI application.

    Sets up a professional dark color scheme optimized for medical imaging
    applications with high contrast and reduced eye strain. Uses varying
    shades of dark grey for hierarchy and subtle contrast.
    """
    # Configure Quasar colors - use dark grey for primary to avoid blue header
    # Note: ui.colors() sets CSS variables that Quasar uses, including --q-primary
    ui.colors(
        primary="#2f2f2f",  # Dark grey - matches header background (--bg-tertiary)
        secondary="#252525",  # Darker grey - matches panel background
        accent="#3a3a3a",  # Medium grey for subtle highlights
        positive="#5cb85c",  # Muted green for success states
        negative="#d9534f",  # Muted red for errors/warnings
        info="#5bc0de",  # Muted cyan for information
        warning="#f0ad4e",  # Muted orange for warnings
    )

    # Remove default padding and margin from the page
    ui.query('.nicegui-content').classes('p-0')

    # Apply CSS variables for dark theme
    ui.add_css('''
        :root {
            --bg-primary: #1a1a1a;
            --bg-secondary: #252525;
            --bg-tertiary: #2f2f2f;
            --bg-quaternary: #3a3a3a;
            --text-primary: #e8e8e8;
            --text-secondary: #a0a0a0;
            --text-disabled: #606060;
            --border-color: #777777;
            --border-subtle: #2a2a2a;
            --border-accent: #5bc0de;
            --shadow: rgba(0, 0, 0, 0.5);
        }

        /* Override Quasar CSS variables for dark mode */
        body.body--dark {
            --q-primary: #2f2f2f;
            --q-secondary: #252525;
            --q-accent: #3a3a3a;
        }
               
        /* Remove default padding and margin from the page */
        body, html, nicegui-content {
            margin: 0;
            padding: 0;
        }

        .q-page {
            min-height: unset !important;
        }

        /* Override default spacing for NiceGUI components */
        .q-pa-none {
            padding: 0 !important;
        }
        .q-ma-none {
            margin: 0 !important;
        }
        .q-gutter-none {
            gap: 0 !important;
        }
    ''')

    # Apply dark theme styles with proper specificity for Quasar components
    ui.add_css('''
        /* Body and main container - dark mode specific */
        body.body--dark {
            background-color: var(--bg-primary) !important;
            color: var(--text-primary) !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        /* Header styling - force dark grey background, overriding Quasar's --q-primary */
        body.body--dark .q-header,
        body.body--dark header.q-header,
        body.body--dark .nicegui-header,
        body.body--dark header.nicegui-header,
        body.body--dark header.q-layout__section--marginal,
        body.body--dark .q-layout__section--marginal.q-header {
            background-color: #2f2f2f !important;
            border-bottom: 1px solid var(--border-color) !important;
        }

        /* Toolbar styling */
        body.body--dark .q-toolbar {
            background-color: var(--bg-tertiary) !important;
        }

        /* Card and panel styling */
        body.body--dark .q-card {
            background-color: var(--bg-secondary) !important;
            border: 1px solid var(--border-color);
        }

        /* Drawer styling */
        body.body--dark .q-drawer {
            background-color: var(--bg-secondary) !important;
            border-right: 1px solid var(--border-color);
        }

        /* Table and grid styling */
        body.body--dark .q-table,
        body.body--dark .ag-theme-quartz-dark {
            background-color: var(--bg-secondary) !important;
            color: var(--text-primary) !important;
        }

        .ag-theme-quartz-dark .ag-row {
        background-color: #1e1e1e !important;
        }
        .ag-theme-quartz-dark .ag-row:nth-child(odd),
        .ag-theme-quartz-dark .ag-row:nth-child(even) {
        background-color: #1e1e1e !important;
        }
        .ag-theme-quartz-dark .ag-row:hover {
        background-color: rgba(255,255,255,0.1) !important;
        }

        /* Input field styling */
        body.body--dark .q-field__control {
            background-color: var(--bg-tertiary) !important;
            color: var(--text-primary) !important;
        }

        /* Button styling */
        body.body--dark .q-btn {
            text-transform: none;
        }

        /* Scrollbar styling for dark theme */
        body.body--dark ::-webkit-scrollbar {
            width: 12px;
            height: 12px;
        }

        body.body--dark ::-webkit-scrollbar-track {
            background: var(--bg-primary);
        }

        body.body--dark ::-webkit-scrollbar-thumb {
            background: var(--bg-tertiary);
            border-radius: 6px;
        }

        body.body--dark ::-webkit-scrollbar-thumb:hover {
            background: #555555;
        }

        /* Slider styling */
        body.body--dark .q-slider__track-container {
            opacity: 0.7;
        }

        /* Menu styling */
        body.body--dark .q-menu {
            background-color: var(--bg-secondary) !important;
            border: 1px solid var(--border-color);
        }

        /* Tab styling */
        body.body--dark .q-tab {
            color: var(--text-secondary) !important;
        }

        body.body--dark .q-tab--active {
            color: var(--text-primary) !important;
        }

        /* Separator styling */
        body.body--dark .q-separator {
            background-color: var(--border-color) !important;
        }

        body.body--dark .q-separator--accent {
            background-color: var(--border-accent) !important;
        }

        /* Tooltip styling */
        body.body--dark .q-tooltip {
            background-color: var(--bg-tertiary) !important;
            color: var(--text-primary) !important;
            font-size: 0.875rem;
        }

        /* Label and text styling */
        body.body--dark .q-item__label {
            color: var(--text-primary) !important;
        }

        /* Dialog styling */
        body.body--dark .q-dialog__backdrop {
            background: rgba(0, 0, 0, 0.7) !important;
        }

        /* Progress bar styling */
        body.body--dark .q-linear-progress {
            background-color: var(--bg-tertiary) !important;
        }
    ''')

    # Color palette constants for programmatic access
    COLORS = {
        "bg_primary": "#1a1a1a",
        "bg_secondary": "#252525",
        "bg_tertiary": "#2f2f2f",
        "bg_quaternary": "#3a3a3a",
        "text_primary": "#e8e8e8",
        "text_secondary": "#a0a0a0",
        "text_disabled": "#606060",
        "border": "#3a3a3a",
        "border_subtle": "#2a2a2a",
        "accent": "#5a5a5a",
        "primary": "#4a4a4a",
        "info": "#5bc0de",
        "warning": "#f0ad4e",
        "error": "#d9534f",
        "success": "#5cb85c",
    }
