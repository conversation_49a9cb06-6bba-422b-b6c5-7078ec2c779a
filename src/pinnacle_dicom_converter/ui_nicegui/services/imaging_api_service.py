"""Imaging API Service for transferring medical imaging data to frontend.

This service provides HTTP endpoints for bulk transfer of CT images, structures (ROIs),
beams, dose distributions, and isodose lines to the Vue.js frontend for client-side
rendering and interaction.

The service is designed for optimal responsiveness by loading all treatment planning
data upfront into the browser, eliminating websocket latency for slice navigation,
window/level adjustments, and overlay rendering.
"""

from typing import Optional, Dict, Any, List
import logging
import numpy as np
import json
from io import BytesIO

from nicegui import app
from fastapi import Response
from fastapi.responses import JSONResponse

from pinnacle_dicom_converter.core.models.image_set import ImageSet
from ..models import (
    ROIDisplayModel,
    POIDisplayModel,
    BeamDisplayModel,
    DoseDisplayModel,
)

logger = logging.getLogger(__name__)


class ImagingAPIService:
    """HTTP API service for transferring imaging data to frontend.

    Provides RESTful endpoints for:
    - CT volume metadata and pixel data
    - ROI structure contours
    - Point of Interest (POI) markers
    - Beam geometries
    - Dose distributions
    - Isodose line definitions

    All data is optimized for efficient transfer with compression and typed arrays.
    """

    def __init__(self):
        """Initialize the imaging API service and register endpoints."""
        # Current dataset references (set by UI components)
        self.current_image_set: Optional[ImageSet] = None
        self.current_rois: List[ROIDisplayModel] = []
        self.current_pois: List[POIDisplayModel] = []
        self.current_beams: List[BeamDisplayModel] = []
        self.current_dose_model: Optional[DoseDisplayModel] = None

        # Register HTTP endpoints with FastAPI
        self._register_endpoints()

        logger.info("ImagingAPIService initialized and endpoints registered")

    def _register_endpoints(self) -> None:
        """Register all HTTP endpoints with the NiceGUI FastAPI app."""

        @app.get('/api/imaging/metadata')
        async def get_metadata() -> JSONResponse:
            """Get CT dataset metadata (dimensions, spacing, origin, window presets).

            Returns:
                JSON response with dataset metadata or error if no dataset loaded
            """
            if not self.current_image_set:
                return JSONResponse(
                    {'error': 'No dataset loaded'},
                    status_code=404
                )

            try:
                # Extract metadata from ImageSet
                image_set = self.current_image_set

                metadata = {
                    'dimensions': {
                        'x': int(image_set.x_dim),
                        'y': int(image_set.y_dim),
                        'z': int(image_set.z_dim)
                    },
                    'spacing': {
                        'x': float(image_set.x_pixdim * 10),  # cm to mm
                        'y': float(image_set.y_pixdim * 10),
                        'z': float(image_set.z_pixdim * 10)
                    },
                    'origin': {
                        'x': float(image_set.x_start * 10),  # cm to mm
                        'y': float(image_set.y_start * 10),
                        'z': float(image_set.z_start * 10)
                    },
                    'dtype': str(image_set.pixel_data.dtype) if hasattr(image_set, 'pixel_data') else 'float32',
                    'sliceCount': int(image_set.z_dim),
                    'windowPresets': [
                        {'name': 'Soft Tissue', 'width': 400, 'level': 40},
                        {'name': 'Lung', 'width': 1500, 'level': -600},
                        {'name': 'Bone', 'width': 2000, 'level': 400},
                        {'name': 'Brain', 'width': 80, 'level': 40},
                        {'name': 'Liver', 'width': 150, 'level': 30}
                    ]
                }

                logger.info(f"Metadata request: {metadata['dimensions']}")
                return JSONResponse(metadata)

            except Exception as e:
                logger.error(f"Error getting metadata: {e}")
                return JSONResponse(
                    {'error': f'Failed to get metadata: {str(e)}'},
                    status_code=500
                )

        @app.get('/api/imaging/volume')
        async def get_full_volume() -> Response:
            """Transfer entire 3D CT dataset as raw binary data.

            Returns binary response with CT volume data and metadata headers.
            FastAPI automatically applies gzip compression for efficient transfer.

            Headers:
                X-Array-Shape: JSON-encoded array shape [z, y, x]
                X-Array-Dtype: NumPy dtype string (e.g., 'float32', 'int16')
                X-Volume-Min: Minimum pixel value
                X-Volume-Max: Maximum pixel value
            """
            if not self.current_image_set or not hasattr(self.current_image_set, 'pixel_data'):
                return JSONResponse(
                    {'error': 'No volume data loaded'},
                    status_code=404
                )

            try:
                volume_data = self.current_image_set.pixel_data

                # Convert to float32 for consistent handling in JavaScript
                if volume_data.dtype != np.float32:
                    volume_data = volume_data.astype(np.float32)

                # Get data statistics
                vmin, vmax = float(volume_data.min()), float(volume_data.max())

                logger.info(
                    f"Volume transfer: shape={volume_data.shape}, "
                    f"size={volume_data.nbytes / 1024 / 1024:.1f}MB, "
                    f"range=[{vmin:.0f}, {vmax:.0f}]"
                )

                return Response(
                    content=volume_data.tobytes(),
                    media_type='application/octet-stream',
                    headers={
                        'X-Array-Shape': json.dumps(list(volume_data.shape)),
                        'X-Array-Dtype': str(volume_data.dtype),
                        'X-Volume-Min': str(vmin),
                        'X-Volume-Max': str(vmax),
                        'Access-Control-Expose-Headers': 'X-Array-Shape, X-Array-Dtype, X-Volume-Min, X-Volume-Max'
                    }
                )

            except Exception as e:
                logger.error(f"Error transferring volume: {e}")
                return JSONResponse(
                    {'error': f'Failed to transfer volume: {str(e)}'},
                    status_code=500
                )

        @app.get('/api/imaging/structures')
        async def get_structures() -> JSONResponse:
            """Get all ROI contours for overlay rendering.

            Returns JSON with list of structures, each containing:
            - id: ROI identifier
            - name: ROI name
            - color: Hex color code
            - visible: Visibility flag
            - contours: List of contour objects with slice_index and points
            """
            try:
                structures = []

                for roi in self.current_rois:
                    structure = {
                        'id': roi.roi_id,
                        'name': roi.name,
                        'color': roi.color,
                        'visible': roi.visible,
                        'contours': []
                    }

                    # Convert contours to serializable format
                    for contour in roi.contours:
                        # contour is a list of (x, y, z) tuples
                        if len(contour) > 0:
                            # Extract z-coordinate from first point to determine slice
                            # (all points in a contour should have same z)
                            z_coord = contour[0][2] if len(contour[0]) >= 3 else 0

                            structure['contours'].append({
                                'z': float(z_coord),
                                'points': [
                                    {'x': float(p[0]), 'y': float(p[1]), 'z': float(p[2])}
                                    for p in contour
                                ]
                            })

                    structures.append(structure)

                logger.info(f"Structures request: {len(structures)} ROIs")
                return JSONResponse({'structures': structures})

            except Exception as e:
                logger.error(f"Error getting structures: {e}")
                return JSONResponse(
                    {'error': f'Failed to get structures: {str(e)}'},
                    status_code=500
                )

        @app.get('/api/imaging/pois')
        async def get_pois() -> JSONResponse:
            """Get all Points of Interest (POIs) for marker rendering.

            Returns JSON with list of POIs, each containing:
            - id: POI identifier
            - name: POI name
            - color: Hex color code
            - visible: Visibility flag
            - position: 3D coordinates {x, y, z}
            """
            try:
                pois = []

                for poi in self.current_pois:
                    pois.append({
                        'id': poi.poi_id,
                        'name': poi.name,
                        'color': poi.color,
                        'visible': poi.visible,
                        'position': {
                            'x': float(poi.position[0]),
                            'y': float(poi.position[1]),
                            'z': float(poi.position[2])
                        }
                    })

                logger.info(f"POIs request: {len(pois)} points")
                return JSONResponse({'pois': pois})

            except Exception as e:
                logger.error(f"Error getting POIs: {e}")
                return JSONResponse(
                    {'error': f'Failed to get POIs: {str(e)}'},
                    status_code=500
                )

        @app.get('/api/imaging/beams')
        async def get_beams() -> JSONResponse:
            """Get beam geometries for visualization.

            Returns JSON with list of beams, each containing:
            - id: Beam identifier
            - name: Beam name
            - color: Hex color code
            - visible: Visibility flag
            - isocenter: 3D isocenter coordinates {x, y, z}
            - gantryAngle: Gantry rotation angle (degrees)
            - couchAngle: Couch rotation angle (degrees)
            - collimatorAngle: Collimator rotation angle (degrees)
            """
            try:
                beams = []

                for beam in self.current_beams:
                    beams.append({
                        'id': beam.beam_id,
                        'name': beam.name,
                        'color': beam.color,
                        'visible': beam.visible,
                        'isocenter': {
                            'x': float(beam.isocenter[0]) if beam.isocenter else 0.0,
                            'y': float(beam.isocenter[1]) if beam.isocenter else 0.0,
                            'z': float(beam.isocenter[2]) if beam.isocenter else 0.0
                        },
                        'gantryAngle': float(beam.gantry_angle) if hasattr(beam, 'gantry_angle') else 0.0,
                        'couchAngle': float(beam.couch_angle) if hasattr(beam, 'couch_angle') else 0.0,
                        'collimatorAngle': float(beam.collimator_angle) if hasattr(beam, 'collimator_angle') else 0.0
                    })

                logger.info(f"Beams request: {len(beams)} beams")
                return JSONResponse({'beams': beams})

            except Exception as e:
                logger.error(f"Error getting beams: {e}")
                return JSONResponse(
                    {'error': f'Failed to get beams: {str(e)}'},
                    status_code=500
                )

        @app.get('/api/imaging/dose')
        async def get_dose() -> Response:
            """Transfer 3D dose distribution as raw binary data.

            Returns binary response with dose grid data and metadata headers.

            Headers:
                X-Array-Shape: JSON-encoded array shape [z, y, x]
                X-Array-Dtype: NumPy dtype string
                X-Dose-Max: Maximum dose value (Gy)
                X-Dose-Prescribe: Prescribed dose (Gy)
                X-Dose-Grid-Origin: JSON-encoded origin coordinates
                X-Dose-Grid-Spacing: JSON-encoded voxel spacing
            """
            if not self.current_dose_model or not hasattr(self.current_dose_model, 'dose_grid'):
                return JSONResponse(
                    {'error': 'No dose data loaded'},
                    status_code=404
                )

            try:
                dose_data = self.current_dose_model.dose_grid

                # Convert to float32 for consistency
                if dose_data.dtype != np.float32:
                    dose_data = dose_data.astype(np.float32)

                max_dose = float(dose_data.max())
                prescribed_dose = float(getattr(self.current_dose_model, 'prescribed_dose', max_dose))

                # Get dose grid geometry (may differ from CT grid)
                origin = getattr(self.current_dose_model, 'origin', [0.0, 0.0, 0.0])
                spacing = getattr(self.current_dose_model, 'spacing', [1.0, 1.0, 1.0])

                logger.info(
                    f"Dose transfer: shape={dose_data.shape}, "
                    f"size={dose_data.nbytes / 1024 / 1024:.1f}MB, "
                    f"max_dose={max_dose:.2f}Gy"
                )

                return Response(
                    content=dose_data.tobytes(),
                    media_type='application/octet-stream',
                    headers={
                        'X-Array-Shape': json.dumps(list(dose_data.shape)),
                        'X-Array-Dtype': str(dose_data.dtype),
                        'X-Dose-Max': str(max_dose),
                        'X-Dose-Prescribe': str(prescribed_dose),
                        'X-Dose-Grid-Origin': json.dumps(list(origin)),
                        'X-Dose-Grid-Spacing': json.dumps(list(spacing)),
                        'Access-Control-Expose-Headers': (
                            'X-Array-Shape, X-Array-Dtype, X-Dose-Max, X-Dose-Prescribe, '
                            'X-Dose-Grid-Origin, X-Dose-Grid-Spacing'
                        )
                    }
                )

            except Exception as e:
                logger.error(f"Error transferring dose: {e}")
                return JSONResponse(
                    {'error': f'Failed to transfer dose: {str(e)}'},
                    status_code=500
                )

        @app.get('/api/imaging/isodose-levels')
        async def get_isodose_levels() -> JSONResponse:
            """Get isodose line definitions for rendering.

            Returns JSON with:
            - levels: List of dose level objects with:
                - percent: Percentage of prescribed dose
                - value: Absolute dose value (Gy)
                - color: Hex color code
                - visible: Visibility flag
            """
            try:
                if not self.current_dose_model:
                    return JSONResponse({'levels': []})

                levels = []
                for isodose in self.current_dose_model.isodose_lines:
                    levels.append({
                        'percent': float(isodose.percent),
                        'value': float(isodose.value),
                        'color': isodose.color,
                        'visible': isodose.visible
                    })

                logger.info(f"Isodose levels request: {len(levels)} levels")
                return JSONResponse({'levels': levels})

            except Exception as e:
                logger.error(f"Error getting isodose levels: {e}")
                return JSONResponse(
                    {'error': f'Failed to get isodose levels: {str(e)}'},
                    status_code=500
                )

    def set_image_set(self, image_set: Optional[ImageSet]) -> None:
        """Update the current image set available to API endpoints.

        Args:
            image_set: ImageSet object with pixel_data, or None to clear
        """
        self.current_image_set = image_set
        logger.info(f"Image set updated: {image_set is not None}")

    def set_structures(self, rois: List[ROIDisplayModel]) -> None:
        """Update the current ROI structures available to API endpoints.

        Args:
            rois: List of ROI display models
        """
        self.current_rois = rois
        logger.info(f"Structures updated: {len(rois)} ROIs")

    def set_pois(self, pois: List[POIDisplayModel]) -> None:
        """Update the current POIs available to API endpoints.

        Args:
            pois: List of POI display models
        """
        self.current_pois = pois
        logger.info(f"POIs updated: {len(pois)} points")

    def set_beams(self, beams: List[BeamDisplayModel]) -> None:
        """Update the current beams available to API endpoints.

        Args:
            beams: List of beam display models
        """
        self.current_beams = beams
        logger.info(f"Beams updated: {len(beams)} beams")

    def set_dose(self, dose_model: Optional[DoseDisplayModel]) -> None:
        """Update the current dose distribution available to API endpoints.

        Args:
            dose_model: Dose display model with grid and isodose lines, or None to clear
        """
        self.current_dose_model = dose_model
        logger.info(f"Dose model updated: {dose_model is not None}")

    def clear_all_data(self) -> None:
        """Clear all current data references."""
        self.current_image_set = None
        self.current_rois = []
        self.current_pois = []
        self.current_beams = []
        self.current_dose_model = None
        logger.info("All imaging data cleared")


# Global singleton instance
imaging_api_service = ImagingAPIService()
