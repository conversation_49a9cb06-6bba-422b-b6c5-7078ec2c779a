"""Pinnacle Service wrapper for UI layer.

This service provides a simplified interface between the UI ViewModels and the
PinnacleAPI, handling error management automatically.
"""

from pathlib import Path
from typing import Optional, List, Tuple
import logging

from pinnacle_dicom_converter.services.pinnacle_api import PinnacleAPI
from pinnacle_dicom_converter.core.models.institution import Institution
from pinnacle_dicom_converter.core.models.patient import Patient
from pinnacle_dicom_converter.core.models.plan import Plan
from pinnacle_dicom_converter.core.models.trial import Trial
from pinnacle_dicom_converter.core.models.image_set import ImageSet
from pinnacle_dicom_converter.core.models.roi import ROI
from pinnacle_dicom_converter.core.models.point import Point
from pinnacle_dicom_converter.core.models.dose import Dose

logger = logging.getLogger(__name__)


class PinnacleService:
    """Service wrapper for PinnacleAPI.

    This service provides a simplified interface for the UI layer.

    Attributes:
        pinnacle_api: The underlying PinnacleAPI instance
        data_source_path: Path to the current data source
        current_institution: Cached institution data
        current_patient: Cached patient data
        current_image_set: Cached image set reference
    """
    
    def __init__(self, data_source_path: Optional[str] = None):
        """Initialize the Pinnacle service.
        
        Args:
            data_source_path: Optional path to Pinnacle data source
        """
        self.pinnacle_api: Optional[PinnacleAPI] = None
        self.data_source_path: Optional[str] = data_source_path
        self.current_institution: Optional[Institution] = None
        self.current_patient: Optional[Patient] = None
        self.current_image_set: Optional[ImageSet] = None
        
        if data_source_path:
            self.load_data_source(data_source_path)
    
    def load_data_source(self, data_source_path: str) -> bool:
        """Load a new data source.
        
        Args:
            data_source_path: Path to Pinnacle data (directory, tar, or zip)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.pinnacle_api = PinnacleAPI(data_source_path)
            self.data_source_path = data_source_path
            
            # Load institution data immediately
            self.current_institution = self.pinnacle_api.get_institution()
            
            # Clear cached data
            self.current_patient = None
            self.current_image_set = None
            
            logger.info(f"Successfully loaded data source: {data_source_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load data source {data_source_path}: {e}")
            self.pinnacle_api = None
            self.data_source_path = None
            self.current_institution = None
            return False
    
    def close_data_source(self) -> None:
        """Close the current data source and clean up resources."""
        if self.pinnacle_api:
            # Clean up the API (context manager cleanup)
            if hasattr(self.pinnacle_api, '__exit__'):
                self.pinnacle_api.__exit__(None, None, None)
        
        self.pinnacle_api = None
        self.data_source_path = None
        self.current_institution = None
        self.current_patient = None
        self.current_image_set = None
        
        logger.info("Data source closed")
    
    def is_data_loaded(self) -> bool:
        """Check if data source is currently loaded."""
        return self.pinnacle_api is not None and self.current_institution is not None
    
    def get_institution(self) -> Optional[Institution]:
        """Get institution data."""
        if not self.is_data_loaded():
            return None
        return self.current_institution
    
    def get_patient(self, patient_id: int) -> Optional[Patient]:
        """Get patient data and cache it for coordinate transformations.
        
        Args:
            patient_id: Patient ID to load
            
        Returns:
            Patient object or None if not found
        """
        if not self.is_data_loaded():
            return None
            
        try:
            patient = self.pinnacle_api.get_patient(self.current_institution, patient_id)
            self.current_patient = patient
            
            # Load the first image set for coordinate transformations
            if patient.image_set_lite_list:
                image_set_id = patient.image_set_lite_list[0].image_set_id
                self.current_image_set = self.pinnacle_api.get_image_header(
                    self.current_institution, patient, image_set_id
                )
            
            return patient
            
        except Exception as e:
            logger.error(f"Failed to load patient {patient_id}: {e}")
            return None
    
    def get_image_set(self, patient_id: int, image_set_id: int, load_pixel_data: bool = True) -> Optional[ImageSet]:
        """Get image set data.
        
        Args:
            patient_id: Patient ID
            image_set_id: Image set ID
            load_pixel_data: Whether to load pixel data (slower but needed for display)
            
        Returns:
            ImageSet object or None if not found
        """
        if not self.is_data_loaded():
            return None
            
        try:
            # Ensure we have the patient loaded
            if not self.current_patient or self.current_patient.patient_id != patient_id:
                patient = self.get_patient(patient_id)
                if not patient:
                    return None
            
            if load_pixel_data:
                image_set = self.pinnacle_api.get_image(
                    self.current_institution, self.current_patient, image_set_id
                )
            else:
                image_set = self.pinnacle_api.get_image_header(
                    self.current_institution, self.current_patient, image_set_id
                )
            
            # Cache for coordinate transformations
            self.current_image_set = image_set
            return image_set
            
        except Exception as e:
            logger.error(f"Failed to load image set {image_set_id}: {e}")
            # For development, try to load without pixel data if that was the issue
            if load_pixel_data:
                logger.info("Retrying without pixel data...")
                try:
                    image_set = self.pinnacle_api.get_image_header(
                        self.current_institution, self.current_patient, image_set_id
                    )
                    if image_set:
                        self.current_image_set = image_set
                        logger.info(f"Loaded image set {image_set_id} for patient {patient_id} (header only)")
                    return image_set
                except Exception as e2:
                    logger.error(f"Failed to load image set header: {e2}")
            return None
    
    def get_rois(self, patient_id: int, plan_id: int) -> List[ROI]:
        """Get ROI data.

        Args:
            patient_id: Patient ID
            plan_id: Plan ID

        Returns:
            List of ROI objects
        """
        if not self.is_data_loaded():
            return []

        try:
            # Ensure we have the patient and image set loaded
            if not self.current_patient or self.current_patient.patient_id != patient_id:
                patient = self.get_patient(patient_id)
                if not patient:
                    return []

            # Find the plan
            plan = None
            for p in self.current_patient.plan_list:
                if p.plan_id == plan_id:
                    plan = p
                    break

            if not plan:
                logger.error(f"Plan {plan_id} not found for patient {patient_id}")
                return []

            # Get ROIs from API
            rois = self.pinnacle_api.get_rois(self.current_institution, self.current_patient, plan)
            return rois

        except Exception as e:
            logger.error(f"Failed to load ROIs for patient {patient_id}, plan {plan_id}: {e}")
            return []
    
    def get_points(self, patient_id: int, plan_id: int) -> List[Point]:
        """Get POI data.

        Args:
            patient_id: Patient ID
            plan_id: Plan ID

        Returns:
            List of Point objects
        """
        if not self.is_data_loaded():
            return []

        try:
            # Ensure we have the patient loaded
            if not self.current_patient or self.current_patient.patient_id != patient_id:
                patient = self.get_patient(patient_id)
                if not patient:
                    return []

            # Find the plan
            plan = None
            for p in self.current_patient.plan_list:
                if p.plan_id == plan_id:
                    plan = p
                    break

            if not plan:
                logger.error(f"Plan {plan_id} not found for patient {patient_id}")
                return []

            # Get points from API
            points = self.pinnacle_api.get_points(self.current_institution, self.current_patient, plan)
            return points

        except Exception as e:
            logger.error(f"Failed to load points for patient {patient_id}, plan {plan_id}: {e}")
            return []
    
    def get_trials(self, patient_id: int, plan_id: int) -> List[Trial]:
        """Get trial data.
        
        Args:
            patient_id: Patient ID
            plan_id: Plan ID
            
        Returns:
            List of Trial objects
        """
        if not self.is_data_loaded():
            return []
            
        try:
            # Ensure we have the patient loaded
            if not self.current_patient or self.current_patient.patient_id != patient_id:
                patient = self.get_patient(patient_id)
                if not patient:
                    return []
            
            # Find the plan
            plan = None
            for p in self.current_patient.plan_list:
                if p.plan_id == plan_id:
                    plan = p
                    break
            
            if not plan:
                logger.error(f"Plan {plan_id} not found for patient {patient_id}")
                return []
            
            # Get trials from API
            trials = self.pinnacle_api.get_trials(self.current_institution, self.current_patient, plan)
            return trials
            
        except Exception as e:
            logger.error(f"Failed to load trials for patient {patient_id}, plan {plan_id}: {e}")
            return []
    
    def get_dose(self, patient_id: int, plan_id: int, trial_id: int) -> Optional[Dose]:
        """Get dose data.

        Args:
            patient_id: Patient ID
            plan_id: Plan ID
            trial_id: Trial ID

        Returns:
            Dose object or None if not found
        """
        if not self.is_data_loaded():
            return None

        try:
            # Get the trial first
            trials = self.get_trials(patient_id, plan_id)
            trial = None
            for t in trials:
                if t.trial_id == trial_id:
                    trial = t
                    break

            if not trial:
                logger.error(f"Trial {trial_id} not found")
                return None

            # Find the plan
            plan = None
            for p in self.current_patient.plan_list:
                if p.plan_id == plan_id:
                    plan = p
                    break

            if not plan:
                logger.error(f"Plan {plan_id} not found")
                return None

            # Get dose from API
            dose = self.pinnacle_api.get_trial_dose(self.current_institution, self.current_patient, plan, trial)
            return dose

        except Exception as e:
            logger.error(f"Failed to load dose for patient {patient_id}, plan {plan_id}, trial {trial_id}: {e}")
            return None
