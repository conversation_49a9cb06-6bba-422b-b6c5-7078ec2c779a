<template>
  <div class="ct-viewer-container"
       @keydown="handleKeyboard"
       @wheel="handleMouseWheel"
       tabindex="0">

    <!-- WebGL canvas for GPU-accelerated rendering -->
    <canvas ref="glCanvas"
            :width="canvasDimensions.width"
            :height="canvasDimensions.height"
            @mousemove="handleMouseMove"
            @click="handleClick"
            class="ct-canvas">
    </canvas>

    <!-- SVG overlay for ROIs, POIs, Dose, Beams -->
    <svg :viewBox="svgViewBox"
         class="ct-overlay"
         v-html="svgContent">
    </svg>

    <!-- Crosshair overlay (optional) -->
    <div v-if="crosshairEnabled" class="crosshair"
         :style="crosshairStyle">
    </div>

    <!-- Performance stats (debug mode) -->
    <div v-if="showStats" class="perf-stats">
      FPS: {{ stats.fps }} | Render: {{ stats.renderTime }}ms | Cache: {{ stats.cacheHits }}/{{ stats.cacheTotal }}
    </div>

    <!-- Loading indicator -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">{{ loadingMessage }}</div>
      <div v-if="loadingProgress > 0" class="loading-progress">
        <div class="loading-progress-bar" :style="{ width: loadingProgress + '%' }"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CTViewer',

  props: {
    // Props passed from Python
    initialSliceIndex: { type: Number, default: 0 },
    initialOrientation: { type: String, default: 'axial' },
    initialWindowWidth: { type: Number, default: 1400 },
    initialWindowLevel: { type: Number, default: 1000 }
  },

  data() {
    return {
      // 3D dataset storage (Z, Y, X) as typed arrays
      imageData3D: null,           // Float32Array for GPU upload
      dimensions: { x: 0, y: 0, z: 0 },
      pixelSpacing: { x: 1, y: 1, z: 1 },
      imageOrigin: { x: 0, y: 0, z: 0 },
      dataRange: { min: 0, max: 4096 }, // For shader normalization

      // Display parameters
      currentSliceIndex: 0,
      orientation: 'axial',         // 'axial', 'sagittal', 'coronal'
      windowWidth: 1400,
      windowLevel: 1000,
      zoomFactor: 1.0,
      panOffset: { x: 0, y: 0 },

      // WebGL state
      gl: null,                     // WebGL context
      useWebGL: true,               // Use WebGL or fallback to Canvas2D
      shaderProgram: null,
      programInfo: null,
      buffers: null,
      textureCache: new Map(),      // GPU texture cache (VRAM)
      maxTextureCache: 50,          // Max textures in cache

      // Rendering state
      renderScheduled: false,       // RAF batching flag
      mouseMoveThrottled: false,    // Mouse move throttle flag

      // SVG overlays
      svgContent: '',

      // Overlay data (loaded from API)
      structures: [],           // ROI contours
      pois: [],                 // Points of Interest
      beams: [],                // Beam geometries
      isodoseLevels: [],        // Isodose line definitions
      doseGrid: null,           // 3D dose distribution (optional)

      // UI state
      crosshairEnabled: false,
      mousePosition: { x: 0, y: 0 },
      worldCoordinates: { x: 0, y: 0, z: 0 },

      // Performance stats
      showStats: false,             // Toggle with 'S' key
      stats: {
        fps: 0,
        renderTime: 0,
        cacheHits: 0,
        cacheTotal: 0
      },
      lastFpsUpdate: 0,
      frameCount: 0,

      // Loading state
      isLoading: false,
      loadingMessage: 'Loading...',
      loadingProgress: 0
    }
  },

  computed: {
    canvasDimensions() {
      return {
        width: this.orientation === 'axial' ? this.dimensions.x :
               this.orientation === 'sagittal' ? this.dimensions.y :
               this.dimensions.x,
        height: this.orientation === 'axial' ? this.dimensions.y :
                this.orientation === 'sagittal' ? this.dimensions.z :
                this.dimensions.z
      };
    },

    svgViewBox() {
      const { width, height } = this.canvasDimensions;
      return `0 0 ${width} ${height}`;
    },

    crosshairStyle() {
      return {
        left: `${this.mousePosition.x}px`,
        top: `${this.mousePosition.y}px`
      };
    }
  },

  methods: {
    // ===== Phase 2.1: WebGL Initialization =====

    /**
     * Initialize WebGL context and shaders
     */
    initWebGL() {
      const canvas = this.$refs.glCanvas;
      const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');

      if (!gl) {
        console.error('WebGL not supported, falling back to Canvas 2D');
        this.useWebGL = false;
        return;
      }

      this.gl = gl;
      this.useWebGL = true;

      // Vertex shader - defines texture quad
      const vertexShaderSource = `
        attribute vec2 a_position;
        attribute vec2 a_texCoord;

        uniform vec2 u_resolution;
        uniform float u_zoom;
        uniform vec2 u_offset;

        varying vec2 v_texCoord;

        void main() {
          // Convert from 0->1 to 0->2, then -1->1 (clip space)
          vec2 position = (a_position * u_zoom + u_offset) / u_resolution * 2.0 - 1.0;
          gl_Position = vec4(position * vec2(1, -1), 0, 1);
          v_texCoord = a_texCoord;
        }
      `;

      // Fragment shader - applies window/level in GPU
      const fragmentShaderSource = `
        precision highp float;

        uniform sampler2D u_texture;
        uniform float u_windowWidth;
        uniform float u_windowLevel;
        uniform float u_minValue;
        uniform float u_maxValue;

        varying vec2 v_texCoord;

        void main() {
          // Sample texture (single channel float)
          float value = texture2D(u_texture, v_texCoord).r;

          // Denormalize from [0,1] to original range
          value = value * (u_maxValue - u_minValue) + u_minValue;

          // Apply window/level
          float windowMin = u_windowLevel - u_windowWidth * 0.5;
          float windowMax = u_windowLevel + u_windowWidth * 0.5;

          float intensity = clamp((value - windowMin) / (windowMax - windowMin), 0.0, 1.0);

          // Grayscale output
          gl_FragColor = vec4(intensity, intensity, intensity, 1.0);
        }
      `;

      // Compile shaders
      this.shaderProgram = this.createShaderProgram(gl, vertexShaderSource, fragmentShaderSource);

      // Get attribute/uniform locations
      this.programInfo = {
        attribLocations: {
          position: gl.getAttribLocation(this.shaderProgram, 'a_position'),
          texCoord: gl.getAttribLocation(this.shaderProgram, 'a_texCoord'),
        },
        uniformLocations: {
          resolution: gl.getUniformLocation(this.shaderProgram, 'u_resolution'),
          zoom: gl.getUniformLocation(this.shaderProgram, 'u_zoom'),
          offset: gl.getUniformLocation(this.shaderProgram, 'u_offset'),
          texture: gl.getUniformLocation(this.shaderProgram, 'u_texture'),
          windowWidth: gl.getUniformLocation(this.shaderProgram, 'u_windowWidth'),
          windowLevel: gl.getUniformLocation(this.shaderProgram, 'u_windowLevel'),
          minValue: gl.getUniformLocation(this.shaderProgram, 'u_minValue'),
          maxValue: gl.getUniformLocation(this.shaderProgram, 'u_maxValue'),
        },
      };

      // Create texture quad buffers
      this.initBuffers();

      // Initialize texture cache (GPU VRAM)
      this.textureCache = new Map();
    },

    /**
     * Create shader program from vertex and fragment shader sources
     */
    createShaderProgram(gl, vsSource, fsSource) {
      const vertexShader = this.compileShader(gl, gl.VERTEX_SHADER, vsSource);
      const fragmentShader = this.compileShader(gl, gl.FRAGMENT_SHADER, fsSource);

      const program = gl.createProgram();
      gl.attachShader(program, vertexShader);
      gl.attachShader(program, fragmentShader);
      gl.linkProgram(program);

      if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        console.error('Shader program link failed:', gl.getProgramInfoLog(program));
        return null;
      }

      return program;
    },

    /**
     * Compile individual shader
     */
    compileShader(gl, type, source) {
      const shader = gl.createShader(type);
      gl.shaderSource(shader, source);
      gl.compileShader(shader);

      if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error('Shader compile failed:', gl.getShaderInfoLog(shader));
        gl.deleteShader(shader);
        return null;
      }

      return shader;
    },

    /**
     * Initialize vertex and texture coordinate buffers
     */
    initBuffers() {
      const gl = this.gl;

      // Quad vertices (2 triangles)
      const positions = new Float32Array([
        0, 0,
        1, 0,
        0, 1,
        0, 1,
        1, 0,
        1, 1,
      ]);

      // Texture coordinates
      const texCoords = new Float32Array([
        0, 0,
        1, 0,
        0, 1,
        0, 1,
        1, 0,
        1, 1,
      ]);

      this.buffers = {
        position: this.createBuffer(gl, positions),
        texCoord: this.createBuffer(gl, texCoords),
      };
    },

    /**
     * Create GPU buffer from data
     */
    createBuffer(gl, data) {
      const buffer = gl.createBuffer();
      gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
      gl.bufferData(gl.ARRAY_BUFFER, data, gl.STATIC_DRAW);
      return buffer;
    },

    // ===== Phase 2.2: Texture Management =====

    /**
     * Get or create GPU texture for slice with LRU caching
     */
    getOrCreateTexture(sliceIndex, orientation) {
      const cacheKey = `${orientation}_${sliceIndex}`;

      // Check GPU texture cache
      if (this.textureCache.has(cacheKey)) {
        this.stats.cacheHits++;
        return this.textureCache.get(cacheKey);
      }

      this.stats.cacheTotal++;

      // Extract slice data
      const sliceData = this.extractSlice(sliceIndex, orientation);

      // Create WebGL texture from Float32Array
      const texture = this.createFloatTexture(sliceData, orientation);

      // Cache in GPU VRAM
      this.textureCache.set(cacheKey, texture);

      // Implement LRU eviction if cache too large
      if (this.textureCache.size > this.maxTextureCache) {
        const firstKey = this.textureCache.keys().next().value;
        const oldTexture = this.textureCache.get(firstKey);
        this.gl.deleteTexture(oldTexture);
        this.textureCache.delete(firstKey);
      }

      return texture;
    },

    /**
     * Create WebGL texture from slice data
     */
    createFloatTexture(data, orientation) {
      const gl = this.gl;
      const { x, y, z } = this.dimensions;

      const width = orientation === 'axial' ? x :
                    orientation === 'sagittal' ? y : x;
      const height = orientation === 'axial' ? y :
                     orientation === 'sagittal' ? z : z;

      // Normalize to 0-1 range for texture
      const normalized = this.normalizeDataForTexture(data);

      const texture = gl.createTexture();
      gl.bindTexture(gl.TEXTURE_2D, texture);

      // Upload to GPU as single-channel float texture
      gl.texImage2D(
        gl.TEXTURE_2D,
        0,                    // mip level
        gl.LUMINANCE,         // internal format (grayscale)
        width,
        height,
        0,                    // border
        gl.LUMINANCE,         // format
        gl.FLOAT,             // type
        normalized
      );

      // Texture parameters (no filtering for medical images)
      gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);
      gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);
      gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
      gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);

      return texture;
    },

    /**
     * Normalize data to 0-1 range for texture upload
     */
    normalizeDataForTexture(data) {
      const min = this.dataRange.min;
      const max = this.dataRange.max;
      const range = max - min;

      const normalized = new Float32Array(data.length);
      for (let i = 0; i < data.length; i++) {
        normalized[i] = (data[i] - min) / range;
      }

      return normalized;
    },

    /**
     * Extract slice data based on orientation
     */
    extractSlice(sliceIndex, orientation) {
      const { x, y, z } = this.dimensions;
      const sliceData = new Float32Array(
        orientation === 'axial' ? x * y :
        orientation === 'sagittal' ? y * z :
        x * z
      );

      // Extract based on orientation (optimized with typed array views)
      if (orientation === 'axial') {
        const offset = sliceIndex * x * y;
        sliceData.set(this.imageData3D.subarray(offset, offset + x * y));
      } else if (orientation === 'sagittal') {
        // Extract X slice
        for (let zi = 0; zi < z; zi++) {
          for (let yi = 0; yi < y; yi++) {
            const srcIdx = zi * x * y + yi * x + sliceIndex;
            const dstIdx = zi * y + yi;
            sliceData[dstIdx] = this.imageData3D[srcIdx];
          }
        }
      } else { // coronal
        // Extract Y slice
        for (let zi = 0; zi < z; zi++) {
          for (let xi = 0; xi < x; xi++) {
            const srcIdx = zi * x * y + sliceIndex * x + xi;
            const dstIdx = zi * x + xi;
            sliceData[dstIdx] = this.imageData3D[srcIdx];
          }
        }
      }

      return sliceData;
    },

    // ===== Phase 2.3: Rendering Pipeline =====

    /**
     * Main render method - called via requestAnimationFrame
     */
    renderCurrentSlice() {
      if (!this.useWebGL) {
        this.renderCanvas2D(); // Fallback
        return;
      }

      const startTime = performance.now();

      const gl = this.gl;
      const texture = this.getOrCreateTexture(this.currentSliceIndex, this.orientation);

      // Clear canvas
      gl.viewport(0, 0, gl.canvas.width, gl.canvas.height);
      gl.clearColor(0, 0, 0, 1);
      gl.clear(gl.COLOR_BUFFER_BIT);

      // Use shader program
      gl.useProgram(this.shaderProgram);

      // Set up position attribute
      gl.bindBuffer(gl.ARRAY_BUFFER, this.buffers.position);
      gl.enableVertexAttribArray(this.programInfo.attribLocations.position);
      gl.vertexAttribPointer(this.programInfo.attribLocations.position, 2, gl.FLOAT, false, 0, 0);

      // Set up texture coordinate attribute
      gl.bindBuffer(gl.ARRAY_BUFFER, this.buffers.texCoord);
      gl.enableVertexAttribArray(this.programInfo.attribLocations.texCoord);
      gl.vertexAttribPointer(this.programInfo.attribLocations.texCoord, 2, gl.FLOAT, false, 0, 0);

      // Bind texture
      gl.activeTexture(gl.TEXTURE0);
      gl.bindTexture(gl.TEXTURE_2D, texture);
      gl.uniform1i(this.programInfo.uniformLocations.texture, 0);

      // Set uniforms
      gl.uniform2f(this.programInfo.uniformLocations.resolution, gl.canvas.width, gl.canvas.height);
      gl.uniform1f(this.programInfo.uniformLocations.zoom, this.zoomFactor);
      gl.uniform2f(this.programInfo.uniformLocations.offset, this.panOffset.x, this.panOffset.y);
      gl.uniform1f(this.programInfo.uniformLocations.windowWidth, this.windowWidth);
      gl.uniform1f(this.programInfo.uniformLocations.windowLevel, this.windowLevel);
      gl.uniform1f(this.programInfo.uniformLocations.minValue, this.dataRange.min);
      gl.uniform1f(this.programInfo.uniformLocations.maxValue, this.dataRange.max);

      // Draw quad (2 triangles = 6 vertices)
      gl.drawArrays(gl.TRIANGLES, 0, 6);

      // Performance tracking
      const renderTime = performance.now() - startTime;
      this.updatePerformanceStats(renderTime);

      // Emit event
      this.$emit('slice_changed', {
        index: this.currentSliceIndex,
        orientation: this.orientation
      });

      // Preload adjacent slices in next idle callback
      if (typeof requestIdleCallback !== 'undefined') {
        requestIdleCallback(() => this.preloadAdjacentSlices());
      }
    },

    /**
     * Batch rendering updates with requestAnimationFrame
     */
    scheduleRender() {
      if (this.renderScheduled) return;

      this.renderScheduled = true;
      requestAnimationFrame(() => {
        this.renderCurrentSlice();
        this.renderScheduled = false;
      });
    },

    /**
     * Update performance statistics
     */
    updatePerformanceStats(renderTime) {
      this.stats.renderTime = renderTime.toFixed(2);

      // Calculate FPS
      const now = performance.now();
      this.frameCount++;

      if (now - this.lastFpsUpdate > 1000) {
        this.stats.fps = Math.round(this.frameCount * 1000 / (now - this.lastFpsUpdate));
        this.frameCount = 0;
        this.lastFpsUpdate = now;
      }
    },

    /**
     * Preload adjacent slices as GPU textures (background)
     */
    preloadAdjacentSlices(range = 5) {
      const maxSlice = this.getMaxSliceForOrientation();

      // Preload in order of likelihood of access
      const indices = [];
      for (let i = 1; i <= range; i++) {
        if (this.currentSliceIndex + i <= maxSlice) {
          indices.push(this.currentSliceIndex + i); // Next slices
        }
        if (this.currentSliceIndex - i >= 0) {
          indices.push(this.currentSliceIndex - i); // Previous slices
        }
      }

      // Load textures in idle time
      indices.forEach((sliceIdx, priority) => {
        if (typeof requestIdleCallback !== 'undefined') {
          requestIdleCallback(() => {
            this.getOrCreateTexture(sliceIdx, this.orientation);
          }, { timeout: 100 * (priority + 1) }); // Lower priority for farther slices
        }
      });
    },

    /**
     * Canvas 2D fallback for non-WebGL browsers
     * Applies window/level transformation on CPU and renders using Canvas 2D API
     */
    renderCanvas2D() {
      if (!this.imageData3D || this.dimensions.x === 0) {
        return; // No data loaded yet
      }

      const startTime = performance.now();

      const canvas = this.$refs.glCanvas;
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        console.error('Cannot get 2D context from canvas');
        return;
      }

      // Clear canvas
      ctx.fillStyle = '#000';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Get current slice dimensions based on orientation
      const width = this.orientation === 'axial' ? this.dimensions.x :
                    this.orientation === 'sagittal' ? this.dimensions.y :
                    this.dimensions.x;
      const height = this.orientation === 'axial' ? this.dimensions.y :
                     this.orientation === 'sagittal' ? this.dimensions.z :
                     this.dimensions.z;

      // Extract slice data
      const sliceData = this.extractSlice(this.currentSliceIndex, this.orientation);

      // Create ImageData for canvas rendering
      const imageData = ctx.createImageData(width, height);
      const pixels = imageData.data;

      // Apply window/level transformation on CPU
      const windowMin = this.windowLevel - this.windowWidth * 0.5;
      const windowMax = this.windowLevel + this.windowWidth * 0.5;

      for (let i = 0; i < sliceData.length; i++) {
        const value = sliceData[i];

        // Apply window/level
        let intensity = (value - windowMin) / (windowMax - windowMin);
        intensity = Math.max(0, Math.min(1, intensity)); // Clamp to [0, 1]

        // Convert to 8-bit grayscale
        const gray = Math.round(intensity * 255);

        // Set RGBA values (grayscale, fully opaque)
        const pixelIndex = i * 4;
        pixels[pixelIndex] = gray;     // R
        pixels[pixelIndex + 1] = gray; // G
        pixels[pixelIndex + 2] = gray; // B
        pixels[pixelIndex + 3] = 255;  // A
      }

      // Calculate zoom/pan transforms
      const scale = this.zoomFactor;
      const offsetX = (canvas.width - width * scale) / 2 + this.panOffset.x;
      const offsetY = (canvas.height - height * scale) / 2 + this.panOffset.y;

      // Save context state
      ctx.save();

      // Apply transforms
      ctx.translate(offsetX, offsetY);
      ctx.scale(scale, scale);

      // Use nearest-neighbor scaling for crisp pixels
      ctx.imageSmoothingEnabled = false;

      // Create temporary canvas for the image data (needed for scaling)
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = width;
      tempCanvas.height = height;
      const tempCtx = tempCanvas.getContext('2d');
      tempCtx.putImageData(imageData, 0, 0);

      // Draw scaled image to main canvas
      ctx.drawImage(tempCanvas, 0, 0);

      // Restore context state
      ctx.restore();

      // Performance tracking
      const renderTime = performance.now() - startTime;
      this.updatePerformanceStats(renderTime);

      // Emit event
      this.$emit('slice_changed', {
        index: this.currentSliceIndex,
        orientation: this.orientation
      });
    },

    // ===== Phase 4: Data Loading =====

    /**
     * Load full 3D dataset from Python (called via run_method)
     * @param {ArrayBuffer} arrayBuffer - Image data as ArrayBuffer
     * @param {{x: number, y: number, z: number}} dimensions - Dataset dimensions
     * @param {{x: number, y: number, z: number}} spacing - Pixel spacing
     * @param {{x: number, y: number, z: number}} origin - Image origin
     */
    loadImageData(arrayBuffer, dimensions, spacing, origin) {
      try {
        // === Data Validation ===

        // Validate arrayBuffer
        if (!arrayBuffer || arrayBuffer.byteLength === 0) {
          throw new Error('Invalid or empty ArrayBuffer provided');
        }

        // Validate dimensions
        if (!dimensions || typeof dimensions !== 'object') {
          throw new Error('Invalid dimensions object');
        }
        if (!Number.isInteger(dimensions.x) || dimensions.x <= 0 ||
            !Number.isInteger(dimensions.y) || dimensions.y <= 0 ||
            !Number.isInteger(dimensions.z) || dimensions.z <= 0) {
          throw new Error(`Invalid dimensions: x=${dimensions.x}, y=${dimensions.y}, z=${dimensions.z}. All must be positive integers.`);
        }

        // Validate spacing
        if (!spacing || typeof spacing !== 'object') {
          throw new Error('Invalid spacing object');
        }
        if (spacing.x <= 0 || spacing.y <= 0 || spacing.z <= 0) {
          throw new Error(`Invalid spacing: x=${spacing.x}, y=${spacing.y}, z=${spacing.z}. All must be positive.`);
        }

        // Validate origin
        if (!origin || typeof origin !== 'object') {
          throw new Error('Invalid origin object');
        }
        if (!isFinite(origin.x) || !isFinite(origin.y) || !isFinite(origin.z)) {
          throw new Error(`Invalid origin: x=${origin.x}, y=${origin.y}, z=${origin.z}. All must be finite numbers.`);
        }

        // Validate data size matches expected dimensions
        const expectedSize = dimensions.x * dimensions.y * dimensions.z;
        const expectedBytes = expectedSize * Float32Array.BYTES_PER_ELEMENT;
        if (arrayBuffer.byteLength !== expectedBytes) {
          throw new Error(
            `Data size mismatch: expected ${expectedBytes} bytes (${expectedSize} Float32 values), ` +
            `got ${arrayBuffer.byteLength} bytes`
          );
        }

        // === Data Loading ===

        // Convert ArrayBuffer to typed array
        this.imageData3D = new Float32Array(arrayBuffer);
        this.dimensions = dimensions;
        this.pixelSpacing = spacing;
        this.imageOrigin = origin;

        // Calculate data range for normalization
        this.calculateDataRange();

        // Validate data range is valid
        if (!isFinite(this.dataRange.min) || !isFinite(this.dataRange.max)) {
          throw new Error(`Invalid data range: min=${this.dataRange.min}, max=${this.dataRange.max}`);
        }
        if (this.dataRange.min === this.dataRange.max) {
          console.warn('Data has uniform values (min === max). Window/level adjustment may not work as expected.');
        }

        // Clear cache and render initial slice
        this.clearTextureCache();

        // Initialize texture cache with new dimensions
        this.textureCache = new Map();

        // Reset view to center slice
        this.currentSliceIndex = Math.floor(this.getMaxSliceForOrientation() / 2);

        // Render initial slice
        if (this.useWebGL && this.gl) {
          this.scheduleRender();
        } else {
          // Fallback to Canvas 2D if WebGL not available
          this.renderCanvas2D();
        }

        console.log(`Loaded dataset: ${dimensions.x}×${dimensions.y}×${dimensions.z}, ` +
                    `range: [${this.dataRange.min.toFixed(2)}, ${this.dataRange.max.toFixed(2)}]`);

        this.$emit('data_loaded', { dimensions, spacing, origin });

      } catch (error) {
        console.error('Failed to load image data:', error.message);
        this.$emit('data_load_error', { error: error.message });
        throw error; // Re-throw to allow Python wrapper to handle
      }
    },

    /**
     * Load all imaging data from HTTP API endpoints
     * This replaces the websocket-based loadImageData approach with HTTP-based bulk transfer
     */
    async loadAllData() {
      try {
        this.isLoading = true;
        this.loadingProgress = 0;
        this.loadingMessage = 'Loading metadata...';
        console.log('Loading imaging data from API endpoints...');

        // Step 1: Load metadata first (10%)
        const metadataResponse = await fetch('/api/imaging/metadata');
        if (!metadataResponse.ok) {
          throw new Error(`Metadata request failed: ${metadataResponse.statusText}`);
        }
        const metadata = await metadataResponse.json();
        this.loadingProgress = 10;

        console.log('Metadata loaded:', metadata);

        // Step 2: Load volume data (largest transfer) (10% -> 80%)
        this.loadingMessage = 'Loading CT volume...';
        await this.loadVolumeFromAPI(metadata);
        this.loadingProgress = 80;

        // Step 3: Load overlay data in parallel (80% -> 100%)
        this.loadingMessage = 'Loading overlays...';
        await this.loadOverlayData();
        this.loadingProgress = 100;

        console.log('All data loaded successfully');

        // Hide loading indicator after short delay
        setTimeout(() => {
          this.isLoading = false;
        }, 500);

      } catch (error) {
        console.error('Failed to load imaging data:', error);
        this.isLoading = false;
        this.loadingMessage = 'Error loading data';
        this.$emit('data_load_error', { error: error.message });
      }
    },

    /**
     * Load CT volume from API endpoint
     */
    async loadVolumeFromAPI(metadata) {
      const response = await fetch('/api/imaging/volume');
      if (!response.ok) {
        throw new Error(`Volume request failed: ${response.statusText}`);
      }

      // Get array metadata from headers
      const shapeHeader = response.headers.get('X-Array-Shape');
      const dtypeHeader = response.headers.get('X-Array-Dtype');
      const minValue = parseFloat(response.headers.get('X-Volume-Min'));
      const maxValue = parseFloat(response.headers.get('X-Volume-Max'));

      if (!shapeHeader || !dtypeHeader) {
        throw new Error('Missing array metadata in response headers');
      }

      const shape = JSON.parse(shapeHeader);

      // Get array buffer from response
      const arrayBuffer = await response.arrayBuffer();

      console.log(`Volume loaded: ${shape.join('×')}, ${(arrayBuffer.byteLength / 1024 / 1024).toFixed(1)}MB`);

      // Convert to Float32Array
      const imageData = new Float32Array(arrayBuffer);

      // Store in component state
      this.imageData3D = imageData;
      this.dimensions = metadata.dimensions;
      this.pixelSpacing = metadata.spacing;
      this.imageOrigin = metadata.origin;
      this.dataRange = { min: minValue, max: maxValue };

      // Clear texture cache
      this.clearTextureCache();
      this.textureCache = new Map();

      // Reset to center slice
      this.currentSliceIndex = Math.floor(this.getMaxSliceForOrientation() / 2);

      // Render initial slice
      if (this.useWebGL && this.gl) {
        this.scheduleRender();
      } else {
        this.renderCanvas2D();
      }

      this.$emit('data_loaded', {
        dimensions: this.dimensions,
        spacing: this.pixelSpacing,
        origin: this.imageOrigin
      });
    },

    /**
     * Load overlay data (structures, POIs, beams, dose) from API endpoints
     */
    async loadOverlayData() {
      try {
        // Load all overlay data in parallel
        const [structuresResponse, poisResponse, beamsResponse, isodoseResponse] = await Promise.all([
          fetch('/api/imaging/structures').then(r => r.ok ? r.json() : { structures: [] }),
          fetch('/api/imaging/pois').then(r => r.ok ? r.json() : { pois: [] }),
          fetch('/api/imaging/beams').then(r => r.ok ? r.json() : { beams: [] }),
          fetch('/api/imaging/isodose-levels').then(r => r.ok ? r.json() : { levels: [] })
        ]);

        // Store overlay data
        this.structures = structuresResponse.structures || [];
        this.pois = poisResponse.pois || [];
        this.beams = beamsResponse.beams || [];
        this.isodoseLevels = isodoseResponse.levels || [];

        // Optionally load dose grid if needed for volume rendering
        // (Skip for now - dose is typically rendered as isodose lines in 2D)

        console.log(`Overlays loaded: ${this.structures.length} ROIs, ${this.pois.length} POIs, ` +
                    `${this.beams.length} beams, ${this.isodoseLevels.length} isodose levels`);

        this.$emit('overlays_loaded', {
          structures: this.structures.length,
          pois: this.pois.length,
          beams: this.beams.length,
          isodoseLevels: this.isodoseLevels.length
        });

      } catch (error) {
        console.error('Failed to load overlay data:', error);
        // Continue without overlays rather than failing completely
      }
    },

    // ===== Helper Methods =====

    /**
     * Get maximum slice index for current orientation
     */
    getMaxSliceForOrientation() {
      if (this.orientation === 'axial') {
        return this.dimensions.z - 1;
      } else if (this.orientation === 'sagittal') {
        return this.dimensions.x - 1;
      } else { // coronal
        return this.dimensions.y - 1;
      }
    },

    /**
     * Calculate data range for normalization
     */
    calculateDataRange() {
      let min = Infinity;
      let max = -Infinity;

      // Sample data for range (full scan is slow)
      const sampleSize = Math.min(10000, this.imageData3D.length);
      const step = Math.floor(this.imageData3D.length / sampleSize);

      for (let i = 0; i < this.imageData3D.length; i += step) {
        const value = this.imageData3D[i];
        if (value < min) min = value;
        if (value > max) max = value;
      }

      this.dataRange = { min, max };
    },

    /**
     * Clear GPU texture cache
     */
    clearTextureCache() {
      if (!this.gl) return;

      this.textureCache.forEach((texture) => {
        this.gl.deleteTexture(texture);
      });
      this.textureCache.clear();
      this.stats.cacheHits = 0;
      this.stats.cacheTotal = 0;
    },

    /**
     * Dispose WebGL resources
     */
    disposeWebGL() {
      if (!this.gl) return;

      this.clearTextureCache();

      if (this.shaderProgram) {
        this.gl.deleteProgram(this.shaderProgram);
      }

      if (this.buffers) {
        this.gl.deleteBuffer(this.buffers.position);
        this.gl.deleteBuffer(this.buffers.texCoord);
      }

      // Lose context to free GPU memory
      const ext = this.gl.getExtension('WEBGL_lose_context');
      if (ext) ext.loseContext();

      this.gl = null;
    },

    // ===== Phase 3.1: Navigation Methods =====

    /**
     * Navigate to a different slice
     * @param {number} delta - Number of slices to move (positive or negative)
     */
    navigateSlice(delta) {
      const maxSlice = this.getMaxSliceForOrientation();
      this.currentSliceIndex = Math.max(0, Math.min(
        this.currentSliceIndex + delta,
        maxSlice
      ));
      this.scheduleRender(); // Batch updates
    },

    /**
     * Change orientation
     * @param {string} newOrientation - Target orientation ('axial', 'sagittal', 'coronal')
     */
    setOrientation(newOrientation) {
      if (this.orientation !== newOrientation) {
        this.orientation = newOrientation;
        this.currentSliceIndex = Math.floor(this.getMaxSliceForOrientation() / 2);
        this.scheduleRender();
      }
    },

    /**
     * Keyboard event handler for navigation and controls
     */
    handleKeyboard(event) {
      const keyActions = {
        'ArrowUp': () => this.navigateSlice(-1),
        'ArrowDown': () => this.navigateSlice(1),
        'PageUp': () => this.navigateSlice(-5),
        'PageDown': () => this.navigateSlice(5),
        'Home': () => {
          this.zoomFactor = 1.0;
          this.panOffset = { x: 0, y: 0 };
          this.scheduleRender();
        },
        '=': () => {
          this.zoomFactor = Math.min(10, this.zoomFactor + 0.1);
          this.scheduleRender();
        },
        '+': () => {
          this.zoomFactor = Math.min(10, this.zoomFactor + 0.1);
          this.scheduleRender();
        },
        '-': () => {
          this.zoomFactor = Math.max(0.1, this.zoomFactor - 0.1);
          this.scheduleRender();
        },
        's': () => {
          this.showStats = !this.showStats;
        },
        'S': () => {
          this.showStats = !this.showStats;
        },
        'c': () => {
          this.crosshairEnabled = !this.crosshairEnabled;
        },
        'C': () => {
          this.crosshairEnabled = !this.crosshairEnabled;
        }
      };

      if (keyActions[event.key]) {
        event.preventDefault();
        keyActions[event.key]();
      }
    },

    /**
     * Mouse wheel handler for slice navigation
     */
    handleMouseWheel(event) {
      event.preventDefault();
      const delta = Math.sign(event.deltaY);
      this.navigateSlice(delta);
    },

    // ===== Phase 3.2: Display Controls =====

    /**
     * Update window/level settings (GPU shader handles the computation)
     * @param {number} width - Window width
     * @param {number} level - Window level (center)
     */
    setWindowLevel(width, level) {
      this.windowWidth = width;
      this.windowLevel = level;
      this.scheduleRender(); // Instant - shader does the work
    },

    /**
     * Mouse move handler with throttling for coordinates
     */
    handleMouseMove(event) {
      if (this.mouseMoveThrottled) return;

      this.mouseMoveThrottled = true;
      requestAnimationFrame(() => {
        const rect = this.$refs.glCanvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // Convert to image coordinates
        const imageCoords = this.screenToImageCoordinates(x, y);
        this.mousePosition = imageCoords;

        // Calculate world coordinates
        this.worldCoordinates = this.imageToWorldCoordinates(imageCoords);

        this.$emit('mouse_move', {
          image: imageCoords,
          world: this.worldCoordinates
        });

        this.mouseMoveThrottled = false;
      });
    },

    /**
     * Convert screen coordinates to image pixel coordinates
     * @param {number} screenX - Screen X coordinate
     * @param {number} screenY - Screen Y coordinate
     * @returns {{x: number, y: number}} Image coordinates
     */
    screenToImageCoordinates(screenX, screenY) {
      const canvas = this.$refs.glCanvas;
      const width = this.orientation === 'axial' ? this.dimensions.x :
                    this.orientation === 'sagittal' ? this.dimensions.y :
                    this.dimensions.x;
      const height = this.orientation === 'axial' ? this.dimensions.y :
                     this.orientation === 'sagittal' ? this.dimensions.z :
                     this.dimensions.z;

      const scale = this.zoomFactor;
      const offsetX = (canvas.width - width * scale) / 2 + this.panOffset.x;
      const offsetY = (canvas.height - height * scale) / 2 + this.panOffset.y;

      const imageX = (screenX - offsetX) / scale;
      const imageY = (screenY - offsetY) / scale;

      return { x: Math.round(imageX), y: Math.round(imageY) };
    },

    /**
     * Convert image pixel coordinates to world coordinates (mm)
     * @param {{x: number, y: number}} imageCoords - Image coordinates
     * @returns {{x: number, y: number, z: number}} World coordinates
     */
    imageToWorldCoordinates(imageCoords) {
      const { x, y } = imageCoords;
      const spacing = this.pixelSpacing;
      const origin = this.imageOrigin;

      // Calculate based on orientation
      if (this.orientation === 'axial') {
        return {
          x: origin.x + x * spacing.x,
          y: origin.y + y * spacing.y,
          z: origin.z + this.currentSliceIndex * spacing.z
        };
      } else if (this.orientation === 'sagittal') {
        return {
          x: origin.x + this.currentSliceIndex * spacing.x,
          y: origin.y + y * spacing.y,
          z: origin.z + x * spacing.z
        };
      } else { // coronal
        return {
          x: origin.x + x * spacing.x,
          y: origin.y + this.currentSliceIndex * spacing.y,
          z: origin.z + y * spacing.z
        };
      }
    },

    /**
     * Click handler
     */
    handleClick(event) {
      // Placeholder for future click interactions (ROI selection, etc.)
      const rect = this.$refs.glCanvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      const imageCoords = this.screenToImageCoordinates(x, y);

      this.$emit('click', {
        image: imageCoords,
        world: this.imageToWorldCoordinates(imageCoords)
      });
    },

    // ===== Phase 3.3: SVG Overlay Management =====

    /**
     * Update SVG overlay content
     * @param {string} svgContent - SVG markup string
     */
    updateSVGOverlay(svgContent) {
      this.svgContent = svgContent;
    },

    /**
     * Clear SVG overlay
     */
    clearSVGOverlay() {
      this.svgContent = '';
    }
  },

  mounted() {
    // Initialize WebGL (Phase 2)
    this.initWebGL();

    // Initialize from props
    this.currentSliceIndex = this.initialSliceIndex;
    this.orientation = this.initialOrientation;
    this.windowWidth = this.initialWindowWidth;
    this.windowLevel = this.initialWindowLevel;

    // Initialize performance tracking
    this.lastFpsUpdate = performance.now();
    this.frameCount = 0;

    // Focus for keyboard events
    this.$el.focus();
  },

  beforeUnmount() {
    // Clean up WebGL resources (Phase 2)
    this.disposeWebGL();
  }
}
</script>

<style scoped>
.ct-viewer-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  overflow: hidden;
}

.ct-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  image-rendering: pixelated; /* Crisp pixel rendering */
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
}

.ct-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.crosshair {
  position: absolute;
  pointer-events: none;
  transform: translate(-50%, -50%);
}

.perf-stats {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: #0f0;
  padding: 5px 10px;
  font-family: monospace;
  font-size: 12px;
  border-radius: 3px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20px;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}

.loading-progress {
  width: 300px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  margin-top: 20px;
  overflow: hidden;
}

.loading-progress-bar {
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s ease;
}
</style>
