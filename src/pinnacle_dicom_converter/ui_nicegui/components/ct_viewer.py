"""Custom CT Viewer Vue Component.

This component provides high-performance CT image viewing with client-side
3D dataset storage to eliminate websocket latency.
"""

from typing import Optional, Callable, Tuple
from pathlib import Path
import numpy as np
from nicegui import ui
from nicegui.element import Element

from pinnacle_dicom_converter.core.models.image_set import ImageSet


class CTViewer(Element, component='ct_viewer.vue'):
    """Custom CT Viewer component with client-side 3D dataset storage.

    This component stores the full 3D CT dataset in the browser's JavaScript
    environment, enabling instant slice navigation and window/level adjustments
    without websocket round-trips.

    Attributes:
        image_set: Current ImageSet being displayed
        on_slice_changed: Callback when slice changes
        on_mouse_move: Callback for mouse movement events
        on_data_loaded: Callback when data finishes loading
    """

    def __init__(
        self,
        initial_slice_index: int = 0,
        initial_orientation: str = 'axial',
        initial_window_width: int = 1400,
        initial_window_level: int = 1000
    ):
        """Initialize CT Viewer component.

        Args:
            initial_slice_index: Starting slice index
            initial_orientation: Starting orientation ('axial', 'sagittal', 'coronal')
            initial_window_width: Starting window width
            initial_window_level: Starting window level
        """
        super().__init__()

        # Set props for Vue component
        self._props['initialSliceIndex'] = initial_slice_index
        self._props['initialOrientation'] = initial_orientation
        self._props['initialWindowWidth'] = initial_window_width
        self._props['initialWindowLevel'] = initial_window_level

        # Event handlers
        self.on_slice_changed: Optional[Callable] = None
        self.on_mouse_move: Optional[Callable] = None
        self.on_data_loaded: Optional[Callable] = None

        # Register event listeners
        self.on('slice_changed', self._handle_slice_changed)
        self.on('mouse_move', self._handle_mouse_move)
        self.on('data_loaded', self._handle_data_loaded)

        # Current state
        self.image_set: Optional[ImageSet] = None
        self._dimensions: Tuple[int, int, int] = (0, 0, 0)

    def load_image_set(self, image_set: ImageSet) -> None:
        """Load 3D image dataset to the Vue component via HTTP API.

        Instead of transferring data through websocket (which has serialization limits),
        this method updates the API service with the new dataset and triggers the Vue
        component to fetch all data via HTTP endpoints.

        Args:
            image_set: ImageSet with pixel_data (numpy array)
        """
        from ..services.imaging_api_service import imaging_api_service

        self.image_set = image_set

        if not hasattr(image_set, 'pixel_data') or image_set.pixel_data is None:
            raise ValueError("ImageSet must have pixel_data attribute")

        # Get dataset properties
        pixel_data = image_set.pixel_data  # numpy array (Z, Y, X)
        self._dimensions = pixel_data.shape

        # Update the API service with this dataset
        imaging_api_service.set_image_set(image_set)

        # Trigger Vue component to fetch data from API endpoints (fire-and-forget)
        # Call async method without awaiting to avoid blocking Python
        try:
            self.client.run_javascript(f'''
                const element = getElement({self.id});
                if (element && element.loadAllData) {{
                    element.loadAllData().catch(err => {{
                        console.error('Failed to load imaging data:', err);
                    }});
                }}
            ''', timeout=0.1)  # Short timeout since we don't need the response
        except Exception:
            # Ignore timeout errors - the Vue component will load data in background
            pass

    def navigate_slice(self, delta: int) -> None:
        """Navigate to a different slice.

        Args:
            delta: Number of slices to move (positive or negative)
        """
        self.run_method('navigateSlice', delta)

    def set_orientation(self, orientation: str) -> None:
        """Change view orientation.

        Args:
            orientation: Target orientation ('axial', 'sagittal', 'coronal')
        """
        if orientation not in ('axial', 'sagittal', 'coronal'):
            raise ValueError(f"Invalid orientation: {orientation}")
        self.run_method('setOrientation', orientation)

    def set_window_level(self, width: int, level: int) -> None:
        """Update window/level settings.

        Args:
            width: Window width
            level: Window level (center)
        """
        self.run_method('setWindowLevel', width, level)

    def set_zoom(self, factor: float) -> None:
        """Set zoom factor.

        Args:
            factor: Zoom factor (1.0 = 100%)
        """
        self._props['zoomFactor'] = factor
        self.update()

    def update_svg_overlay(self, svg_content: str) -> None:
        """Update SVG overlay content.

        Args:
            svg_content: SVG markup string
        """
        self.run_method('updateSVGOverlay', svg_content)

    def clear_svg_overlay(self) -> None:
        """Clear SVG overlay."""
        self.run_method('clearSVGOverlay')

    def update_overlay_data(self, rois=None, pois=None, beams=None, dose_model=None) -> None:
        """Update overlay data in the API service and trigger Vue component refresh.

        Args:
            rois: List of ROI display models (optional)
            pois: List of POI display models (optional)
            beams: List of beam display models (optional)
            dose_model: Dose display model with isodose lines (optional)
        """
        from ..services.imaging_api_service import imaging_api_service

        if rois is not None:
            imaging_api_service.set_structures(rois)
        if pois is not None:
            imaging_api_service.set_pois(pois)
        if beams is not None:
            imaging_api_service.set_beams(beams)
        if dose_model is not None:
            imaging_api_service.set_dose(dose_model)

        # Trigger Vue component to refresh overlay data (fire-and-forget)
        try:
            self.client.run_javascript(f'''
                const element = getElement({self.id});
                if (element && element.loadOverlayData) {{
                    element.loadOverlayData().catch(err => {{
                        console.error('Failed to load overlay data:', err);
                    }});
                }}
            ''', timeout=0.1)  # Short timeout since we don't need the response
        except Exception:
            # Ignore timeout errors - the Vue component will load data in background
            pass

    def preload_adjacent_slices(self, range: int = 5) -> None:
        """Preload slices around current position.

        Args:
            range: Number of slices to preload in each direction
        """
        self.run_method('preloadAdjacentSlices', range)

    # Event handlers
    def _handle_slice_changed(self, e) -> None:
        """Handle slice changed event from Vue."""
        if self.on_slice_changed:
            self.on_slice_changed(e.args['index'], e.args['orientation'])

    def _handle_mouse_move(self, e) -> None:
        """Handle mouse move event from Vue."""
        if self.on_mouse_move:
            self.on_mouse_move(e.args['image'], e.args['world'])

    def _handle_data_loaded(self, e) -> None:
        """Handle data loaded event from Vue."""
        if self.on_data_loaded:
            self.on_data_loaded(e.args)
