"""Toolbar ViewModel for application header controls."""

from dataclasses import dataclass
from typing import Optional, Callable
from pathlib import Path
from nicegui import ui


@dataclass
class ToolbarViewModel:
    """ViewModel for header toolbar controls and state management.

    Manages the state of toolbar buttons (enabled/disabled) and handles
    user actions from the toolbar menu items.

    Attributes:
        has_data_loaded: Whether a Pinnacle dataset is currently loaded
        is_selection_complete: Whether patient/plan/trial selection is complete
        on_data_loaded: Callback invoked when data is successfully loaded
        on_data_closed: Callback invoked when dataset is closed
    """

    # State properties
    has_data_loaded: bool = False
    is_selection_complete: bool = False

    # Callbacks for state changes
    on_data_loaded: Optional[Callable[[Path], None]] = None
    on_data_closed: Optional[Callable[[], None]] = None

    # Callback for actions
    on_open_directory: Optional[Callable[[], None]] = None
    on_open_archive: Optional[Callable[[], None]] = None
    on_save_dicom_local: Optional[Callable[[], None]] = None
    on_export_dicom_network: Optional[Callable[[], None]] = None
    on_show_help: Optional[Callable[[], None]] = None

    def open_directory(self) -> None:
        """Handle 'Open Directory' menu action.

        Opens native file picker for selecting a Pinnacle directory.
        On selection, triggers data loading workflow.
        """
        if self.on_open_directory:
            self.on_open_directory()
        else:
            # Placeholder implementation - will be replaced in Phase 8
            ui.notify("Open Directory - Integration pending (Phase 8)", type="info")

    def open_archive(self) -> None:
        """Handle 'Open Archive' menu action.

        Opens native file picker for selecting a Pinnacle archive
        (.tar, .tar.gz, .zip). On selection, triggers data loading workflow.
        """
        if self.on_open_archive:
            self.on_open_archive()
        else:
            # Placeholder implementation - will be replaced in Phase 8
            ui.notify("Open Archive - Integration pending (Phase 8)", type="info")

    def close_dataset(self) -> None:
        """Handle 'Close Dataset' menu action.

        Closes the currently loaded Pinnacle dataset and resets
        application state.
        """
        self.has_data_loaded = False
        self.is_selection_complete = False

        if self.on_data_closed:
            self.on_data_closed()

    def save_dicom_local(self) -> None:
        """Handle 'Save DICOM Files Locally' button action.

        Opens directory picker and saves generated DICOM files
        to the selected location. Requires complete patient/plan/trial selection.
        """
        if self.on_save_dicom_local:
            self.on_save_dicom_local()
        else:
            # Placeholder implementation - will be replaced in Phase 8
            ui.notify("Save DICOM Locally - Integration pending (Phase 8)", type="info")

    def export_dicom_network(self) -> None:
        """Handle 'Export DICOM to Network' button action.

        Exports generated DICOM files to configured network DICOM
        server (PACS). Requires complete patient/plan/trial selection.
        """
        if self.on_export_dicom_network:
            self.on_export_dicom_network()
        else:
            # Placeholder implementation - will be replaced in Phase 8
            ui.notify("Export DICOM to Network - Integration pending (Phase 8)", type="info")

    def show_help(self) -> None:
        """Handle 'Help & About' button action.

        Displays help dialog with keyboard shortcuts and application
        information.
        """
        if self.on_show_help:
            self.on_show_help()
        else:
            # Placeholder implementation - will be replaced in Phase 10
            ui.notify("Help Dialog - Implementation pending (Phase 10)", type="info")

    def reset(self) -> None:
        """Reset toolbar state to initial values.

        Called when dataset is closed or application is reset.
        """
        self.has_data_loaded = False
        self.is_selection_complete = False