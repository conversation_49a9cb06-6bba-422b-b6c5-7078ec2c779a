"""Image ViewModel for CT display.

This ViewModel manages CT image display state including orientation, slice navigation,
window/level adjustments, and coordinate tracking. It provides the interface between
the UI components and the ImageService for data access.
"""

from typing import Optional, Callable, TYPE_CHECKING
import logging

from nicegui.events import KeyEventArguments

from pinnacle_dicom_converter.core.models.image_set import ImageSet
from pinnacle_dicom_converter.ui_nicegui.services.image_service import ImageService
from pinnacle_dicom_converter.ui_nicegui.services.pinnacle_service import PinnacleService

if TYPE_CHECKING:
    from pinnacle_dicom_converter.ui_nicegui.components.ct_viewer import CTViewer

logger = logging.getLogger(__name__)


class ImageViewModel:
    """ViewModel for CT image display.

    Manages all aspects of CT image display including:
    - Image data and metadata
    - Display parameters (orientation, window/level, zoom)
    - Slice navigation
    - Coordinate tracking
    - SVG overlay content preparation
    - Keyboard shortcut handling

    Attributes:
        current_slice_index: Current slice being displayed (0-based)
        total_slices: Total number of slices in the image set
        window_width: CT window width for display
        window_level: CT window level (center) for display
        zoom_factor: Zoom level (1.0 = 100%)
        current_orientation: Current view orientation ('axial', 'sagittal', 'coronal')
        image_info_text: Descriptive text about current image
        coordinate_text: Current mouse coordinate display text
        current_image_url: Data URL for the current image
        svg_content: SVG overlay content for ROIs, POIs, etc.
    """

    def __init__(self, pinnacle_service: Optional[PinnacleService] = None):
        """Initialize the image ViewModel with default values.

        Args:
            pinnacle_service: Optional PinnacleService instance for data access
        """
        # Services
        self.pinnacle_service = pinnacle_service
        self.image_service = ImageService(pinnacle_service.pinnacle_api if pinnacle_service else None)

        # CTViewer component reference (set by UI view during initialization)
        self.ct_viewer_component: Optional['CTViewer'] = None

        # Image data
        self.image_set: Optional[ImageSet] = None
        self.current_slice_index: int = 0
        self.total_slices: int = 0

        # Display parameters
        self.current_orientation: str = "axial"
        self.window_width: int = 1400
        self.window_level: int = 1000
        self.zoom_factor: float = 1.0

        # Image URL for ui.interactive_image
        self.current_image_url: Optional[str] = None

        # SVG overlay content
        self.svg_content: str = ""

        # Display text
        self.image_info_text: str = "No image loaded"
        self.coordinate_text: str = ""

        # Coordinate tracking
        self.pixel_spacing: tuple = (1.0, 1.0, 1.0)
        self.image_origin: tuple = (0.0, 0.0, 0.0)

        # Callbacks
        self.on_image_changed: Optional[Callable[[], None]] = None
        self._update_orientation_styles: Optional[Callable[[], None]] = None

    def load_image_set(self, image_set: Optional[ImageSet]) -> None:
        """Load CT image set to Vue component.

        Args:
            image_set: ImageSet object from PinnacleAPI (None for placeholder)
        """
        self.image_set = image_set

        if image_set is not None:
            # Get image metadata from service
            dimensions = self.image_service.get_image_dimensions(image_set)
            self.total_slices = dimensions[2] if dimensions else 0
            self.current_slice_index = self.total_slices // 2  # Start at middle slice
            self.pixel_spacing = self.image_service.get_pixel_spacing(image_set)
            self.image_origin = self.image_service.get_image_origin(image_set)

            # Update info text
            self.image_info_text = (
                f"CT - {dimensions[0]}×{dimensions[1]}×{dimensions[2]} - "
                f"Spacing: {self.pixel_spacing[0]:.2f}×{self.pixel_spacing[1]:.2f}×{self.pixel_spacing[2]:.2f}mm"
            )

            # Load to Vue component (one-time transfer)
            if self.ct_viewer_component:
                self.ct_viewer_component.load_image_set(image_set)
        else:
            # Load placeholder data
            self.total_slices = 100
            self.current_slice_index = 50
            self.pixel_spacing = (1.0, 1.0, 3.0)
            self.image_origin = (0.0, 0.0, 0.0)
            self.image_info_text = "Mock CT - 512×512×100 - Spacing: 1.0×1.0×3.0mm"

    def load_mock_image_set(self) -> None:
        """Load mock image set for UI testing."""
        self.load_image_set(None)

    def set_orientation(self, orientation: str) -> None:
        """Change view orientation via Vue component.

        Args:
            orientation: Target orientation ('axial', 'sagittal', 'coronal')
        """
        if orientation in ("axial", "sagittal", "coronal"):
            if self.ct_viewer_component:
                self.ct_viewer_component.set_orientation(orientation)
            self.current_orientation = orientation

            # Update orientation styles if the function exists
            if hasattr(self, '_update_orientation_styles') and self._update_orientation_styles is not None:
                self._update_orientation_styles()

    def navigate_slice(self, delta: int) -> None:
        """Navigate to different slice by delta (for keyboard shortcuts).

        Note: The Vue component handles its own navigation via keyboard events.
        This method is kept for potential programmatic navigation from Python.

        Args:
            delta: Number of slices to move (positive or negative)
        """
        if self.ct_viewer_component:
            self.ct_viewer_component.navigate_slice(delta)

    def update_window_level(self) -> None:
        """Update window/level via Vue component.

        The Vue component handles window/level adjustments in GPU shaders
        for instant visual feedback without websocket round-trips.
        """
        if self.ct_viewer_component:
            self.ct_viewer_component.set_window_level(
                self.window_width,
                self.window_level
            )

    def reset_zoom(self) -> None:
        """Reset zoom to 100% via Vue component."""
        self.zoom_factor = 1.0
        if self.ct_viewer_component:
            self.ct_viewer_component.set_zoom(1.0)

    def on_keyboard_event(self, e: KeyEventArguments) -> None:
        """Handle keyboard shortcuts.

        Note: The Vue component handles most keyboard events directly.
        This method is kept for potential custom keyboard shortcuts from Python.

        Args:
            e: KeyEventArguments object containing key and action
        """
        # Vue component handles navigation internally, but we keep this for compatibility
        pass

    def on_mouse_move(self, image_x: float, image_y: float) -> None:
        """Update coordinate display on mouse move (legacy method).

        Note: This is now handled by on_mouse_move_from_vue() which receives
        world coordinates directly from the Vue component.

        Args:
            image_x: X coordinate in image pixels
            image_y: Y coordinate in image pixels
        """
        # Legacy compatibility - Vue component handles coordinate conversion
        pass

    def on_slice_changed_from_vue(self, index: int, orientation: str) -> None:
        """Handle slice change event from Vue component.

        Args:
            index: New slice index
            orientation: Current orientation
        """
        self.current_slice_index = index
        self.current_orientation = orientation

        # Update overlays if needed
        self.update_svg_overlays()

    def on_mouse_move_from_vue(self, image_coords: dict, world_coords: dict) -> None:
        """Handle mouse move event from Vue component.

        Args:
            image_coords: Dictionary with 'x' and 'y' image pixel coordinates
            world_coords: Dictionary with 'x', 'y', 'z' world coordinates (mm)
        """
        x, y, z = world_coords['x'], world_coords['y'], world_coords['z']
        self.coordinate_text = f"({x:.1f}, {y:.1f}, {z:.1f})"

    def on_data_loaded_from_vue(self, data: dict) -> None:
        """Handle data loaded event from Vue component.

        Args:
            data: Dictionary with dimensions, spacing, origin information
        """
        logger.info(f"Data loaded in Vue component: {data}")

    def update_svg_overlays(self, overlay_vm=None) -> None:
        """Generate SVG overlay content from overlay ViewModel.

        Args:
            overlay_vm: OverlayViewModel instance with ROI/POI/Beam/Dose data
        """
        if overlay_vm is None:
            self.svg_content = ""
            return

        try:
            # Import SVG converters
            from ..converters import (
                ROISVGConverter, POISVGConverter,
                DoseSVGConverter, BeamSVGConverter
            )

            # Collect all SVG content
            svg_parts = []

            # Get image parameters for converters
            if self.image_set:
                pixel_spacing = self.image_service.get_pixel_spacing(self.image_set)
                image_origin = self.image_service.get_image_origin(self.image_set)
                image_dimensions = self.image_service.get_image_dimensions(self.image_set)
            else:
                # Use default values if no image set
                pixel_spacing = self.pixel_spacing
                image_origin = self.image_origin
                image_dimensions = (512, 512, 100)

            # Create converters with current image parameters
            roi_converter = ROISVGConverter(
                pixel_spacing=pixel_spacing,
                image_origin=image_origin,
                image_dimensions=image_dimensions,
                zoom_factor=self.zoom_factor,
                current_slice=self.current_slice_index,
                orientation=self.current_orientation
            )

            poi_converter = POISVGConverter(
                pixel_spacing=pixel_spacing,
                image_origin=image_origin,
                image_dimensions=image_dimensions,
                zoom_factor=self.zoom_factor,
                current_slice=self.current_slice_index,
                orientation=self.current_orientation
            )

            dose_converter = DoseSVGConverter(
                pixel_spacing=pixel_spacing,
                image_origin=image_origin,
                image_dimensions=image_dimensions,
                zoom_factor=self.zoom_factor,
                current_slice=self.current_slice_index,
                orientation=self.current_orientation
            )

            beam_converter = BeamSVGConverter(
                pixel_spacing=pixel_spacing,
                image_origin=image_origin,
                image_dimensions=image_dimensions,
                zoom_factor=self.zoom_factor,
                current_slice=self.current_slice_index,
                orientation=self.current_orientation
            )

            # Convert ROIs to SVG
            if overlay_vm.visible_rois:
                roi_svg = roi_converter.convert_rois_to_svg(overlay_vm.visible_rois)
                if roi_svg:
                    svg_parts.append(roi_svg)

            # Convert POIs to SVG
            if overlay_vm.visible_pois:
                poi_svg = poi_converter.convert_pois_to_svg(overlay_vm.visible_pois)
                if poi_svg:
                    svg_parts.append(poi_svg)

            # Convert dose/isodose to SVG
            if overlay_vm.dose_model and overlay_vm.visible_isodose_lines:
                dose_svg = dose_converter.convert_dose_to_svg(overlay_vm.dose_model)
                if dose_svg:
                    svg_parts.append(dose_svg)

            # Convert beams to SVG
            if overlay_vm.visible_beams:
                beam_svg = beam_converter.convert_beams_to_svg(overlay_vm.visible_beams)
                if beam_svg:
                    svg_parts.append(beam_svg)

            # Combine all SVG content
            if svg_parts:
                # Create master SVG container
                width, height = roi_converter.get_image_bounds()
                master_svg = [roi_converter.create_svg_header(width, height)]

                # Add all overlay content (remove individual headers/footers)
                for svg_part in svg_parts:
                    # Extract content between <svg> and </svg> tags
                    start_idx = svg_part.find('>') + 1
                    end_idx = svg_part.rfind('</svg>')
                    if start_idx > 0 and end_idx > start_idx:
                        content = svg_part[start_idx:end_idx].strip()
                        if content:
                            master_svg.append(content)

                master_svg.append(roi_converter.create_svg_footer())
                self.svg_content = '\n'.join(master_svg)

                # Update Vue component overlay
                if self.ct_viewer_component:
                    self.ct_viewer_component.update_svg_overlay(self.svg_content)
            else:
                self.svg_content = ""
                if self.ct_viewer_component:
                    self.ct_viewer_component.clear_svg_overlay()

        except Exception as e:
            logger.error(f"Failed to update SVG overlays: {e}")
            self.svg_content = ""
            if self.ct_viewer_component:
                self.ct_viewer_component.clear_svg_overlay()

    def reset(self) -> None:
        """Reset all state to initial values."""
        # Clear Vue component state
        if self.ct_viewer_component:
            self.ct_viewer_component.clear_svg_overlay()

        self.image_set = None
        self.current_slice_index = 0
        self.total_slices = 0
        self.current_orientation = "axial"
        self.window_width = 1400
        self.window_level = 1000
        self.zoom_factor = 1.0
        self.current_image_url = None
        self.svg_content = ""
        self.image_info_text = "No image loaded"
        self.coordinate_text = ""
        self.pixel_spacing = (1.0, 1.0, 1.0)
        self.image_origin = (0.0, 0.0, 0.0)
