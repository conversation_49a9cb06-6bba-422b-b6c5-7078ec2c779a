"""Navigation ViewModel for patient/plan/trial selection."""

from typing import Optional, Callable
import pandas as pd
import logging

from ..models.patient_display import PatientDisplayModel
from ..models.plan_display import PlanDisplayModel
from ..models.trial_display import TrialDisplayModel
from ..services.pinnacle_service import PinnacleService

logger = logging.getLogger(__name__)


class NavigationViewModel:
    """ViewModel for patient/plan/trial navigation.

    Manages the state and behavior of the left drawer navigation system,
    including patient, plan, and trial selection with cascading data loading.

    Attributes:
        patients_df: DataFrame for patients AgGrid
        plans_df: DataFrame for plans AgGrid
        trials_df: DataFrame for trials AgGrid
        selected_patient_id: Currently selected patient ID
        selected_plan_id: Currently selected plan ID
        selected_trial_id: Currently selected trial ID
    """

    def __init__(self, pinnacle_service: Optional[PinnacleService] = None):
        """Initialize the navigation ViewModel with service injection.

        Args:
            pinnacle_service: Service for accessing Pinnacle data
        """
        # Injected service
        self.pinnacle_service = pinnacle_service

        # DataFrames for AgGrid (reactive)
        self.patients_df: Optional[pd.DataFrame] = None
        self.plans_df: Optional[pd.DataFrame] = None
        self.trials_df: Optional[pd.DataFrame] = None

        # Current selections
        self.selected_patient_id: Optional[int] = None
        self.selected_plan_id: Optional[int] = None
        self.selected_trial_id: Optional[int] = None

        # Store full data models for lookup
        self._patients: list[PatientDisplayModel] = []
        self._plans: list[PlanDisplayModel] = []
        self._trials: list[TrialDisplayModel] = []

        # Label text for UI binding
        self.patient_label_text: str = "Patient:"
        self.plan_label_text: str = "Plan:"
        self.trial_label_text: str = "Trial:"

        # Callbacks to notify other ViewModels
        self.on_patient_changed: Optional[Callable[[int], None]] = None
        self.on_plan_changed: Optional[Callable[[int], None]] = None
        self.on_trial_changed: Optional[Callable[[int], None]] = None

        # Refresh callbacks (set by views)
        self.refresh_patients: Optional[Callable[[], None]] = None
        self.refresh_plans: Optional[Callable[[], None]] = None
        self.refresh_trials: Optional[Callable[[], None]] = None

    @property
    def selected_patient(self) -> Optional[PatientDisplayModel]:
        """Get the currently selected patient model.

        Returns:
            PatientDisplayModel if a patient is selected, None otherwise.
        """
        if self.selected_patient_id is None:
            return None
        return next(
            (p for p in self._patients if p.patient_id == self.selected_patient_id),
            None,
        )

    @property
    def selected_plan(self) -> Optional[PlanDisplayModel]:
        """Get the currently selected plan model.

        Returns:
            PlanDisplayModel if a plan is selected, None otherwise.
        """
        if self.selected_plan_id is None:
            return None
        return next(
            (p for p in self._plans if p.plan_id == self.selected_plan_id),
            None,
        )

    @property
    def selected_trial(self) -> Optional[TrialDisplayModel]:
        """Get the currently selected trial model.

        Returns:
            TrialDisplayModel if a trial is selected, None otherwise.
        """
        if self.selected_trial_id is None:
            return None
        return next(
            (t for t in self._trials if t.trial_id == self.selected_trial_id),
            None,
        )

    def _update_label_texts(self) -> None:
        """Update label text attributes based on current selections."""
        # Update patient label
        patient = self.selected_patient
        if patient is not None:
            self.patient_label_text = f"Patient {patient.patient_id}: {patient.last_name}, {patient.first_name} ({patient.medical_record_number})"
        else:
            self.patient_label_text = "Patient:"

        # Update plan label
        plan = self.selected_plan
        if plan is not None:
            self.plan_label_text = f"Plan {plan.plan_id}: {plan.plan_name}"
        else:
            self.plan_label_text = "Plan:"

        # Update trial label
        trial = self.selected_trial
        if trial is not None:
            self.trial_label_text = f"Trial {trial.trial_id}: {trial.trial_name}"
        else:
            self.trial_label_text = "Trial:"

    def load_mock_data(self) -> None:
        """Load mock patient data for testing.

        Creates a realistic dataset with:
        - 3 patients
        - 2-3 plans per patient
        - 2-4 trials per plan
        """
        # Create mock patients
        self._patients = [
            PatientDisplayModel(
                patient_id=1,
                first_name="John",
                last_name="Smith",
                medical_record_number="MRN001234",
            ),
            PatientDisplayModel(
                patient_id=2,
                first_name="Jane",
                last_name="Doe",
                medical_record_number="MRN005678",
            ),
            PatientDisplayModel(
                patient_id=3,
                first_name="Robert",
                last_name="Johnson",
                medical_record_number="MRN009012",
            ),
        ]

        # Create mock plans
        self._plans = [
            # Patient 1 plans
            PlanDisplayModel(plan_id=1, plan_name="Primary Treatment", patient_id=1),
            PlanDisplayModel(plan_id=2, plan_name="Boost Plan", patient_id=1),
            PlanDisplayModel(plan_id=3, plan_name="Re-Plan", patient_id=1),
            # Patient 2 plans
            PlanDisplayModel(plan_id=4, plan_name="Initial Plan", patient_id=2),
            PlanDisplayModel(plan_id=5, plan_name="Revised Plan", patient_id=2),
            # Patient 3 plans
            PlanDisplayModel(plan_id=6, plan_name="Primary IMRT", patient_id=3),
            PlanDisplayModel(plan_id=7, plan_name="VMAT Plan", patient_id=3),
            PlanDisplayModel(plan_id=8, plan_name="3D Conformal", patient_id=3),
        ]

        # Create mock trials
        self._trials = [
            # Plan 1 trials
            TrialDisplayModel(trial_id=1, trial_name="Trial 1", num_beams=5, plan_id=1),
            TrialDisplayModel(trial_id=2, trial_name="Trial 2", num_beams=7, plan_id=1),
            TrialDisplayModel(trial_id=3, trial_name="QA", num_beams=5, plan_id=1),
            # Plan 2 trials
            TrialDisplayModel(trial_id=4, trial_name="Trial 1", num_beams=3, plan_id=2),
            TrialDisplayModel(trial_id=5, trial_name="Trial 2", num_beams=4, plan_id=2),
            # Plan 3 trials
            TrialDisplayModel(trial_id=6, trial_name="Trial 1", num_beams=9, plan_id=3),
            TrialDisplayModel(trial_id=7, trial_name="Trial 2", num_beams=9, plan_id=3),
            TrialDisplayModel(trial_id=8, trial_name="Optimization", num_beams=7, plan_id=3),
            # Plan 4 trials
            TrialDisplayModel(trial_id=9, trial_name="Trial 1", num_beams=6, plan_id=4),
            TrialDisplayModel(trial_id=10, trial_name="Trial 2", num_beams=6, plan_id=4),
            TrialDisplayModel(trial_id=11, trial_name="Final", num_beams=6, plan_id=4),
            # Plan 5 trials
            TrialDisplayModel(trial_id=12, trial_name="Trial 1", num_beams=5, plan_id=5),
            TrialDisplayModel(trial_id=13, trial_name="Trial 2", num_beams=5, plan_id=5),
            # Plan 6 trials
            TrialDisplayModel(trial_id=14, trial_name="Trial 1", num_beams=7, plan_id=6),
            TrialDisplayModel(trial_id=15, trial_name="Trial 2", num_beams=9, plan_id=6),
            TrialDisplayModel(trial_id=16, trial_name="Trial 3", num_beams=9, plan_id=6),
            # Plan 7 trials
            TrialDisplayModel(trial_id=17, trial_name="Arc 1", num_beams=2, plan_id=7),
            TrialDisplayModel(trial_id=18, trial_name="Arc 2", num_beams=2, plan_id=7),
            TrialDisplayModel(trial_id=19, trial_name="Full VMAT", num_beams=2, plan_id=7),
            # Plan 8 trials
            TrialDisplayModel(trial_id=20, trial_name="Trial 1", num_beams=4, plan_id=8),
            TrialDisplayModel(trial_id=21, trial_name="Trial 2", num_beams=5, plan_id=8),
        ]

        # Load patients DataFrame
        self.load_patients()

    def load_patients_from_service(self) -> None:
        """Load patients from PinnacleService."""
        if not self.pinnacle_service or not self.pinnacle_service.is_data_loaded():
            logger.warning("No data source loaded")
            self._patients = []
            self.load_patients()
            return

        try:
            institution = self.pinnacle_service.get_institution()
            if not institution or not hasattr(institution, 'patient_lite_list'):
                logger.warning("No patients found in institution")
                self._patients = []
                self.load_patients()
                return

            # Convert to display models
            self._patients = []
            for patient_lite in institution.patient_lite_list:
                display_model = PatientDisplayModel(
                    patient_id=patient_lite.patient_id,
                    first_name=getattr(patient_lite, 'first_name', 'Unknown'),
                    last_name=getattr(patient_lite, 'last_name', 'Patient'),
                    medical_record_number=getattr(patient_lite, 'medical_record_number', 'N/A')
                )
                self._patients.append(display_model)

            logger.info(f"Loaded {len(self._patients)} patients from service")
            self.load_patients()

        except Exception as e:
            logger.error(f"Failed to load patients from service: {e}")
            self._patients = []
            self.load_patients()

    def load_patients(self) -> None:
        """Load patients into DataFrame for AgGrid display."""
        if not self._patients:
            self.patients_df = None
        else:
            patient_data = [
                {
                    "id": p.patient_id,
                    "mrn": p.medical_record_number,
                    "name": p.full_name,
                }
                for p in self._patients
            ]
            self.patients_df = pd.DataFrame(patient_data)

        # Trigger UI refresh if callback is set
        if self.refresh_patients:
            self.refresh_patients()

    def on_patient_selected(self, selected_row) -> None:
        """Handle patient selection from AgGrid.

        Args:
            selected_row: Selected row from AgGrid containing patient data
        """
        if selected_row:
            self.selected_patient_id = selected_row.get("id")

            # Clear downstream selections
            self.selected_plan_id = None
            self.selected_trial_id = None

            # Load plans for selected patient
            self.load_plans()

            # Clear trials since no plan is selected
            self.trials_df = None
            if self.refresh_trials:
                self.refresh_trials()

            # Update label texts for UI bindings
            self._update_label_texts()

            # Notify other ViewModels
            if self.on_patient_changed and self.selected_patient_id is not None:
                self.on_patient_changed(self.selected_patient_id)

    def load_plans(self) -> None:
        """Load plans for the selected patient into DataFrame."""
        if self.selected_patient_id is None:
            self.plans_df = None
            self._plans = []
        else:
            # Load plans from service
            self._load_plans_from_service()

            # Filter plans for selected patient
            patient_plans = [
                p for p in self._plans if p.patient_id == self.selected_patient_id
            ]

            if not patient_plans:
                self.plans_df = None
            else:
                plan_data = [
                    {
                        "id": plan.plan_id,
                        "name": plan.plan_name,
                    }
                    for plan in patient_plans
                ]
                self.plans_df = pd.DataFrame(plan_data)

        # Trigger UI refresh if callback is set
        if self.refresh_plans:
            self.refresh_plans()

    def _load_plans_from_service(self) -> None:
        """Load plans from PinnacleService for the selected patient."""
        if (not self.pinnacle_service or
            not self.pinnacle_service.is_data_loaded() or
            self.selected_patient_id is None):
            self._plans = []
            return

        try:
            # Get patient data from service
            patient = self.pinnacle_service.get_patient(self.selected_patient_id)
            if not patient or not hasattr(patient, 'plan_list'):
                logger.warning(f"No plans found for patient {self.selected_patient_id}")
                self._plans = []
                return

            # Convert to display models
            self._plans = []
            for plan in patient.plan_list:
                display_model = PlanDisplayModel(
                    plan_id=plan.plan_id,
                    plan_name=getattr(plan, 'plan_name', f'Plan {plan.plan_id}'),
                    patient_id=self.selected_patient_id
                )
                self._plans.append(display_model)

            logger.info(f"Loaded {len(self._plans)} plans for patient {self.selected_patient_id}")

        except Exception as e:
            logger.error(f"Failed to load plans for patient {self.selected_patient_id}: {e}")
            self._plans = []

    def on_plan_selected(self, selected_row) -> None:
        """Handle plan selection from AgGrid.

        Args:
            selected_row: Selected row from AgGrid containing plan data
        """
        if selected_row:
            self.selected_plan_id = selected_row.get("id")

            # Clear downstream selections
            self.selected_trial_id = None

            # Load trials for selected plan
            self.load_trials()

            # Update label texts for UI bindings
            self._update_label_texts()

            # Notify other ViewModels
            if self.on_plan_changed and self.selected_plan_id is not None:
                self.on_plan_changed(self.selected_plan_id)

    def load_trials(self) -> None:
        """Load trials for the selected plan into DataFrame."""
        if self.selected_plan_id is None:
            self.trials_df = None
            self._trials = []
        else:
            # Load trials from service
            self._load_trials_from_service()

            # Filter trials for selected plan
            plan_trials = [
                t for t in self._trials if t.plan_id == self.selected_plan_id
            ]

            if not plan_trials:
                self.trials_df = None
            else:
                trial_data = [
                    {
                        "id": trial.trial_id,
                        "name": trial.trial_name,
                        "beams": trial.num_beams,
                    }
                    for trial in plan_trials
                ]
                self.trials_df = pd.DataFrame(trial_data)

        # Trigger UI refresh if callback is set
        if self.refresh_trials:
            self.refresh_trials()

    def _load_trials_from_service(self) -> None:
        """Load trials from PinnacleService for the selected plan."""
        if (not self.pinnacle_service or
            not self.pinnacle_service.is_data_loaded() or
            self.selected_patient_id is None or
            self.selected_plan_id is None):
            self._trials = []
            return

        try:
            # Get trials from service
            trials = self.pinnacle_service.get_trials(self.selected_patient_id, self.selected_plan_id)

            # Convert to display models
            self._trials = []
            for trial in trials:
                if trial is None or trial.trial_id is None:
                    logger.warning(f"Trial {trial} is None or trial_id is None")
                    continue
                num_beams = len(trial.beam_list) if hasattr(trial, 'beam_list') and trial.beam_list else 0
                display_model = TrialDisplayModel(
                    trial_id=trial.trial_id,
                    trial_name=getattr(trial, 'name', f'Trial {trial.trial_id}'),
                    num_beams=num_beams,
                    plan_id=self.selected_plan_id
                )
                self._trials.append(display_model)

            logger.info(f"Loaded {len(self._trials)} trials for plan {self.selected_plan_id}")

        except Exception as e:
            logger.error(f"Failed to load trials for plan {self.selected_plan_id}: {e}")
            self._trials = []

    def on_trial_selected(self, selected_row) -> None:
        """Handle trial selection from AgGrid.

        Args:
            selected_row: Selected row from AgGrid containing trial data
        """
        if selected_row:
            self.selected_trial_id = selected_row.get("id")

            # Update label texts for UI bindings
            self._update_label_texts()

            # Notify other ViewModels
            if self.on_trial_changed and self.selected_trial_id is not None:
                self.on_trial_changed(self.selected_trial_id)

    def reset(self) -> None:
        """Reset all state to initial values."""
        self.patients_df = None
        self.plans_df = None
        self.trials_df = None
        self.selected_patient_id = None
        self.selected_plan_id = None
        self.selected_trial_id = None
        self._patients = []
        self._plans = []
        self._trials = []

        # Reset label texts
        self.patient_label_text = "Patient:"
        self.plan_label_text = "Plan:"
        self.trial_label_text = "Trial:"

        # Trigger all refreshes
        if self.refresh_patients:
            self.refresh_patients()
        if self.refresh_plans:
            self.refresh_plans()
        if self.refresh_trials:
            self.refresh_trials()