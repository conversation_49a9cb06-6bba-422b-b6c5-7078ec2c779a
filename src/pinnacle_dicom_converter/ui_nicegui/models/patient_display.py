"""Patient display model for UI rendering."""

from dataclasses import dataclass


@dataclass
class PatientDisplayModel:
    """Simplified patient model for UI display.

    Contains only the essential patient information needed for the
    navigation grid and patient selection.

    Attributes:
        patient_id: Unique patient identifier
        first_name: <PERSON><PERSON>'s first name
        last_name: <PERSON><PERSON>'s last name
        medical_record_number: Medical record number (MRN)
    """

    patient_id: int
    first_name: str
    last_name: str
    medical_record_number: str

    @property
    def full_name(self) -> str:
        """Get patient's full name in "Last, First" format."""
        return f"{self.last_name}, {self.first_name}"