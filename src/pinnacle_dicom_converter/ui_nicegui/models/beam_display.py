"""Beam display model for UI rendering."""

from dataclasses import dataclass
from typing import Optional


@dataclass
class BeamDisplayModel:
    """Display model for radiation beam with UI-specific properties.

    Attributes:
        beam_id: Unique identifier for the beam
        name: Display name of the beam
        color: Hex color code for rendering (e.g., '#0000FF')
        visible: Whether the beam should be displayed
        gantry_angle: Gantry angle in degrees
        collimator_angle: Collimator angle in degrees
        couch_angle: Couch angle in degrees
        energy: Beam energy in MV or MeV
        modality: Treatment modality ('Photon', 'Electron', 'Proton')
        line_width: Width of beam line in pixels
    """

    beam_id: int
    name: str
    color: str = "#0000FF"
    visible: bool = True
    gantry_angle: Optional[float] = None
    collimator_angle: Optional[float] = None
    couch_angle: Optional[float] = None
    energy: Optional[str] = None
    modality: str = "Photon"
    line_width: int = 2
