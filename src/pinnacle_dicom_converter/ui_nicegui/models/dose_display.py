"""Dose display model for UI rendering."""

from dataclasses import dataclass, field
from typing import List, Tuple, Optional


@dataclass
class IsodoseDisplayModel:
    """Display model for isodose line with UI-specific properties.

    Attributes:
        level: Dose level as percentage of reference dose
        color: Hex color code for rendering
        visible: Whether the isodose line should be displayed
        contours: List of contour point lists (x, y, z coordinates)
        line_width: Width of isodose line in pixels
        label: Display label for the isodose level
    """

    level: float
    color: str
    visible: bool = True
    contours: Optional[List[List[Tuple[float, float, float]]]] = None
    line_width: int = 2
    label: Optional[str] = None

    def __post_init__(self):
        """Initialize contours list and label if None."""
        if self.contours is None:
            self.contours = []
        if self.label is None:
            self.label = f"{self.level:.0f}%"


@dataclass
class DoseDisplayModel:
    """Display model for dose grid with UI-specific properties.

    Attributes:
        reference_dose_cgy: Reference dose in cGy
        isodose_lines: List of isodose line display models
        show_dose_grid: Whether to show dose grid
        dose_opacity: Opacity for dose colorwash (0.0 to 1.0)
    """

    reference_dose_cgy: float = 3000.0
    isodose_lines: List[IsodoseDisplayModel] = field(default_factory=list)
    show_dose_grid: bool = False
    dose_opacity: float = 0.5
