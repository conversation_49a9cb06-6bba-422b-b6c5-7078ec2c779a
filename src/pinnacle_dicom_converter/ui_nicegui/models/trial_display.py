"""Trial display model for UI rendering."""

from dataclasses import dataclass


@dataclass
class TrialDisplayModel:
    """Simplified trial model for UI display.

    Contains only the essential trial information needed for the
    navigation grid and trial selection.

    Attributes:
        trial_id: Unique trial identifier
        trial_name: Descriptive name of the trial
        num_beams: Number of beams in this trial
        plan_id: Parent plan identifier
    """

    trial_id: int
    trial_name: str
    num_beams: int
    plan_id: int