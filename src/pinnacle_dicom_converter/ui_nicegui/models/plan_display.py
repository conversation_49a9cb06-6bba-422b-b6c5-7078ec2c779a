"""Plan display model for UI rendering."""

from dataclasses import dataclass


@dataclass
class PlanDisplayModel:
    """Simplified plan model for UI display.

    Contains only the essential plan information needed for the
    navigation grid and plan selection.

    Attributes:
        plan_id: Unique plan identifier
        plan_name: Descriptive name of the treatment plan
        patient_id: Parent patient identifier
    """

    plan_id: int
    plan_name: str
    patient_id: int