"""POI display model for UI rendering."""

from dataclasses import dataclass
from typing import Tuple, Optional


@dataclass
class POIDisplayModel:
    """Display model for Point of Interest with UI-specific properties.

    Attributes:
        poi_id: Unique identifier for the POI
        name: Display name of the POI
        color: Hex color code for rendering (e.g., '#00FF00')
        visible: Whether the POI should be displayed
        coordinates: (x, y, z) coordinates in mm
        marker_style: Style of marker ('circle', 'cross', 'diamond', 'square')
        marker_size: Size of marker in pixels
    """

    poi_id: int
    name: str
    color: str = "#00FF00"
    visible: bool = True
    coordinates: Optional[Tuple[float, float, float]] = None
    marker_style: str = "circle"
    marker_size: int = 8
