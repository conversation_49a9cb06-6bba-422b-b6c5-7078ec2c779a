"""ROI display model for UI rendering."""

from dataclasses import dataclass, field
from typing import List, Tuple, Optional


@dataclass
class ROIDisplayModel:
    """Display model for ROI with UI-specific properties.

    Attributes:
        roi_id: Unique identifier for the ROI
        name: Display name of the ROI
        color: Hex color code for rendering (e.g., '#FF0000')
        visible: Whether the ROI should be displayed
        contours: List of contour point lists (x, y, z coordinates)
        opacity: Transparency level (0.0 to 1.0)
        line_width: Width of contour lines in pixels
    """

    roi_id: int
    name: str
    color: str = "#FF0000"
    visible: bool = True
    contours: Optional[List[List[Tuple[float, float, float]]]] = None
    opacity: float = 1.0
    line_width: int = 2

    def __post_init__(self):
        """Initialize contours list if None."""
        if self.contours is None:
            self.contours = []
