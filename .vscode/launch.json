{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: CLI Export",
            "type": "debugpy",
            "request": "launch",
            "cwd": "${workspaceFolder}",
            "module": "pinnacle_dicom_converter.cli.main",
            "console": "integratedTerminal",
            "args": [
                "export",
                "--input",
                "tests/data/01",
                "--patient-id", 
                "1",
                "--plan-id",
                "0",
                "--trial-id",
                "0",
                "--output",
                "tests/data/01/DicomTest"
            ]
        },
        {
            "name": "Python Debugger: NiceGUI",
            "type": "debugpy",
            "request": "launch",
            "cwd": "${workspaceFolder}",
            "module": "pinnacle_dicom_converter.ui_nicegui.main",
            "console": "integratedTerminal",
            "args": [
                "--reload",
                "--log-level",
                "DEBUG"
            ]
        },
        {
            "name": "Python Debugger: PySide6 Desktop GUI",
            "type": "debugpy",
            "request": "launch",
            "cwd": "${workspaceFolder}",
            "module": "pinnacle_dicom_converter.ui_pyside.main",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": [
                "--log-level",
                "DEBUG"
            ]
        },
        {
            "name": "Python Debugger: Desktop GUI (via CLI)",
            "type": "debugpy",
            "request": "launch",
            "cwd": "${workspaceFolder}",
            "module": "pinnacle_dicom_converter.cli.main",
            "console": "integratedTerminal",
            "args": [
                "desktop"
            ],
            "justMyCode": false
        }
    ]
}