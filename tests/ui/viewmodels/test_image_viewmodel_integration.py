"""Integration tests for ImageViewModel with CT Viewer component.

This module tests the integration between ImageViewModel and the CTViewer
Vue component, verifying proper event handling and method invocation.
"""

import pytest
from unittest.mock import Mock, MagicMock, patch
import numpy as np

from pinnacle_dicom_converter.ui_nicegui.viewmodels.image_viewmodel import ImageViewModel
from pinnacle_dicom_converter.ui_nicegui.components.ct_viewer import CTViewer
from pinnacle_dicom_converter.core.models.image_set import ImageSet


@pytest.fixture
def mock_pinnacle_service():
    """Create a mock PinnacleService."""
    service = Mock()
    service.pinnacle_api = Mock()
    return service


@pytest.fixture
def mock_ct_viewer():
    """Create a mock CTViewer component."""
    with patch.object(CTViewer, 'on'):
        viewer = CTViewer()
        viewer.run_method = Mock()
        viewer.load_image_set = Mock()
        viewer.set_orientation = Mock()
        viewer.set_window_level = Mock()
        viewer.set_zoom = Mock()
        viewer.update_svg_overlay = Mock()
        viewer.clear_svg_overlay = Mock()
        viewer.navigate_slice = Mock()
        viewer.preload_adjacent_slices = Mock()
        return viewer


@pytest.fixture
def image_viewmodel(mock_pinnacle_service):
    """Create an ImageViewModel instance."""
    return ImageViewModel(pinnacle_service=mock_pinnacle_service)


@pytest.fixture
def sample_image_set():
    """Create a sample ImageSet for testing."""
    image_set = Mock(spec=ImageSet)
    image_set.pixel_data = np.random.randint(-1000, 3000, size=(100, 512, 512), dtype=np.int16)
    image_set.x_dim = 512
    image_set.y_dim = 512
    image_set.z_dim = 100
    image_set.x_pixdim = 0.1  # cm
    image_set.y_pixdim = 0.1  # cm
    image_set.z_pixdim = 0.3  # cm
    image_set.x_start = -25.6  # cm
    image_set.y_start = -25.6  # cm
    image_set.z_start = -30.0  # cm
    return image_set


class TestImageViewModelCTViewerIntegration:
    """Test integration between ImageViewModel and CTViewer."""

    def test_ct_viewer_component_reference(self, image_viewmodel, mock_ct_viewer):
        """Test that CT viewer component reference is stored correctly."""
        image_viewmodel.ct_viewer_component = mock_ct_viewer

        assert image_viewmodel.ct_viewer_component == mock_ct_viewer

    def test_load_image_set_calls_component(self, image_viewmodel, mock_ct_viewer, sample_image_set):
        """Test that loading image set calls component's load_image_set."""
        image_viewmodel.ct_viewer_component = mock_ct_viewer

        # Mock image service methods
        image_viewmodel.image_service.get_image_dimensions = Mock(return_value=(512, 512, 100))
        image_viewmodel.image_service.get_pixel_spacing = Mock(return_value=(1.0, 1.0, 3.0))
        image_viewmodel.image_service.get_image_origin = Mock(return_value=(-256.0, -256.0, -300.0))

        image_viewmodel.load_image_set(sample_image_set)

        # Verify component method was called
        mock_ct_viewer.load_image_set.assert_called_once_with(sample_image_set)

        # Verify state updated
        assert image_viewmodel.image_set == sample_image_set
        assert image_viewmodel.total_slices == 100
        assert image_viewmodel.current_slice_index == 50  # Middle slice

    def test_set_orientation_calls_component(self, image_viewmodel, mock_ct_viewer):
        """Test that setting orientation calls component method."""
        image_viewmodel.ct_viewer_component = mock_ct_viewer

        image_viewmodel.set_orientation('sagittal')

        mock_ct_viewer.set_orientation.assert_called_once_with('sagittal')
        assert image_viewmodel.current_orientation == 'sagittal'

    def test_set_orientation_invalid_ignored(self, image_viewmodel, mock_ct_viewer):
        """Test that invalid orientation is ignored."""
        image_viewmodel.ct_viewer_component = mock_ct_viewer

        image_viewmodel.set_orientation('invalid')

        # Should not call component method
        mock_ct_viewer.set_orientation.assert_not_called()
        # Orientation should not change
        assert image_viewmodel.current_orientation == 'axial'

    def test_navigate_slice_calls_component(self, image_viewmodel, mock_ct_viewer):
        """Test that navigating slice calls component method."""
        image_viewmodel.ct_viewer_component = mock_ct_viewer

        image_viewmodel.navigate_slice(5)

        mock_ct_viewer.navigate_slice.assert_called_once_with(5)

    def test_update_window_level_calls_component(self, image_viewmodel, mock_ct_viewer):
        """Test that updating window/level calls component method."""
        image_viewmodel.ct_viewer_component = mock_ct_viewer
        image_viewmodel.window_width = 2000
        image_viewmodel.window_level = 500

        image_viewmodel.update_window_level()

        mock_ct_viewer.set_window_level.assert_called_once_with(2000, 500)

    def test_reset_zoom_calls_component(self, image_viewmodel, mock_ct_viewer):
        """Test that resetting zoom calls component method."""
        image_viewmodel.ct_viewer_component = mock_ct_viewer

        image_viewmodel.reset_zoom()

        mock_ct_viewer.set_zoom.assert_called_once_with(1.0)
        assert image_viewmodel.zoom_factor == 1.0

    def test_on_slice_changed_from_vue_updates_state(self, image_viewmodel):
        """Test that slice changed event from Vue updates ViewModel state."""
        image_viewmodel.on_slice_changed_from_vue(25, 'axial')

        assert image_viewmodel.current_slice_index == 25
        assert image_viewmodel.current_orientation == 'axial'

    def test_on_mouse_move_from_vue_updates_coordinates(self, image_viewmodel):
        """Test that mouse move event from Vue updates coordinate display."""
        image_coords = {'x': 256, 'y': 256}
        world_coords = {'x': -12.8, 'y': 56.7, 'z': 123.4}

        image_viewmodel.on_mouse_move_from_vue(image_coords, world_coords)

        assert image_viewmodel.coordinate_text == "(-12.8, 56.7, 123.4)"

    def test_on_data_loaded_from_vue_logs_info(self, image_viewmodel, caplog):
        """Test that data loaded event from Vue logs information."""
        data = {
            'dimensions': {'x': 512, 'y': 512, 'z': 100},
            'spacing': {'x': 1.0, 'y': 1.0, 'z': 3.0}
        }

        with caplog.at_level('INFO'):
            image_viewmodel.on_data_loaded_from_vue(data)

        assert "Data loaded in Vue component" in caplog.text

    def test_update_svg_overlays_with_component(self, image_viewmodel, mock_ct_viewer):
        """Test that updating SVG overlays with None clears content."""
        image_viewmodel.ct_viewer_component = mock_ct_viewer
        image_viewmodel.image_set = None  # No image set

        image_viewmodel.update_svg_overlays(overlay_vm=None)

        # When overlay_vm is None, it returns early and just sets svg_content to ""
        # The component is not called in this case
        assert image_viewmodel.svg_content == ""
        mock_ct_viewer.clear_svg_overlay.assert_not_called()

    def test_reset_clears_component_state(self, image_viewmodel, mock_ct_viewer):
        """Test that reset clears component SVG overlay."""
        image_viewmodel.ct_viewer_component = mock_ct_viewer

        image_viewmodel.reset()

        mock_ct_viewer.clear_svg_overlay.assert_called_once()
        assert image_viewmodel.image_set is None
        assert image_viewmodel.current_slice_index == 0
        assert image_viewmodel.total_slices == 0


class TestImageViewModelWithoutComponent:
    """Test ImageViewModel behavior when component is not set."""

    def test_load_image_set_without_component(self, image_viewmodel, sample_image_set):
        """Test loading image set when component is not set (should not crash)."""
        # Component is None
        assert image_viewmodel.ct_viewer_component is None

        # Mock image service methods
        image_viewmodel.image_service.get_image_dimensions = Mock(return_value=(512, 512, 100))
        image_viewmodel.image_service.get_pixel_spacing = Mock(return_value=(1.0, 1.0, 3.0))
        image_viewmodel.image_service.get_image_origin = Mock(return_value=(-256.0, -256.0, -300.0))

        # Should not raise exception
        image_viewmodel.load_image_set(sample_image_set)

        # State should still be updated
        assert image_viewmodel.image_set == sample_image_set

    def test_set_orientation_without_component(self, image_viewmodel):
        """Test setting orientation when component is not set."""
        assert image_viewmodel.ct_viewer_component is None

        # Should not raise exception
        image_viewmodel.set_orientation('sagittal')

        # State should be updated
        assert image_viewmodel.current_orientation == 'sagittal'

    def test_navigate_slice_without_component(self, image_viewmodel):
        """Test navigating slice when component is not set."""
        assert image_viewmodel.ct_viewer_component is None

        # Should not raise exception
        image_viewmodel.navigate_slice(5)

    def test_update_window_level_without_component(self, image_viewmodel):
        """Test updating window/level when component is not set."""
        assert image_viewmodel.ct_viewer_component is None

        image_viewmodel.window_width = 2000
        image_viewmodel.window_level = 500

        # Should not raise exception
        image_viewmodel.update_window_level()

    def test_reset_without_component(self, image_viewmodel):
        """Test reset when component is not set."""
        assert image_viewmodel.ct_viewer_component is None

        # Should not raise exception
        image_viewmodel.reset()


class TestEventHandlerCallbacks:
    """Test event handler callbacks are properly invoked."""

    def test_slice_changed_triggers_overlay_update(self, image_viewmodel, mock_ct_viewer):
        """Test that slice changed event triggers overlay update."""
        image_viewmodel.ct_viewer_component = mock_ct_viewer

        # Mock update_svg_overlays to verify it's called
        with patch.object(image_viewmodel, 'update_svg_overlays') as mock_update:
            image_viewmodel.on_slice_changed_from_vue(30, 'axial')

            mock_update.assert_called_once()

    def test_orientation_change_updates_styles(self, image_viewmodel, mock_ct_viewer):
        """Test that orientation change updates orientation styles if callback exists."""
        image_viewmodel.ct_viewer_component = mock_ct_viewer

        # Set the callback
        mock_callback = Mock()
        image_viewmodel._update_orientation_styles = mock_callback

        image_viewmodel.set_orientation('sagittal')

        mock_callback.assert_called_once()

    def test_orientation_change_without_style_callback(self, image_viewmodel, mock_ct_viewer):
        """Test orientation change works without style callback."""
        image_viewmodel.ct_viewer_component = mock_ct_viewer

        # Callback is None
        image_viewmodel._update_orientation_styles = None

        # Should not raise exception
        image_viewmodel.set_orientation('sagittal')

        assert image_viewmodel.current_orientation == 'sagittal'


class TestMockImageSetLoading:
    """Test loading mock/placeholder image sets."""

    def test_load_mock_image_set(self, image_viewmodel):
        """Test loading mock image set for UI testing."""
        image_viewmodel.load_mock_image_set()

        assert image_viewmodel.total_slices == 100
        assert image_viewmodel.current_slice_index == 50
        assert image_viewmodel.pixel_spacing == (1.0, 1.0, 3.0)
        assert "Mock CT" in image_viewmodel.image_info_text

    def test_load_none_image_set(self, image_viewmodel):
        """Test loading None as image set."""
        image_viewmodel.load_image_set(None)

        assert image_viewmodel.total_slices == 100
        assert image_viewmodel.current_slice_index == 50
        assert "Mock CT" in image_viewmodel.image_info_text


class TestStateConsistency:
    """Test state consistency between ViewModel and Component."""

    def test_state_consistency_after_load(self, image_viewmodel, mock_ct_viewer, sample_image_set):
        """Test state is consistent after loading."""
        image_viewmodel.ct_viewer_component = mock_ct_viewer

        # Mock image service methods
        image_viewmodel.image_service.get_image_dimensions = Mock(return_value=(512, 512, 100))
        image_viewmodel.image_service.get_pixel_spacing = Mock(return_value=(1.0, 1.0, 3.0))
        image_viewmodel.image_service.get_image_origin = Mock(return_value=(-256.0, -256.0, -300.0))

        image_viewmodel.load_image_set(sample_image_set)

        # Verify component was called
        mock_ct_viewer.load_image_set.assert_called_once()

        # Verify ViewModel state
        assert image_viewmodel.image_set == sample_image_set
        assert image_viewmodel.total_slices == 100
        assert image_viewmodel.pixel_spacing == (1.0, 1.0, 3.0)

    def test_state_consistency_after_orientation_change(self, image_viewmodel, mock_ct_viewer):
        """Test state is consistent after orientation change."""
        image_viewmodel.ct_viewer_component = mock_ct_viewer

        # Change orientation
        image_viewmodel.set_orientation('coronal')

        # Verify component was called
        mock_ct_viewer.set_orientation.assert_called_once_with('coronal')

        # Verify ViewModel state matches
        assert image_viewmodel.current_orientation == 'coronal'

    def test_state_consistency_after_slice_change(self, image_viewmodel):
        """Test state is consistent after slice change from Vue."""
        initial_slice = image_viewmodel.current_slice_index

        image_viewmodel.on_slice_changed_from_vue(75, 'axial')

        assert image_viewmodel.current_slice_index == 75
        assert image_viewmodel.current_slice_index != initial_slice
