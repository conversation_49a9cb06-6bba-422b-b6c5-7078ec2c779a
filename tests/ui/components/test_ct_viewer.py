"""Unit tests for CT Viewer component.

This module tests the Python wrapper for the CT Viewer Vue component,
including initialization, data loading, event handling, and method invocation.
"""

import pytest
import numpy as np
from unittest.mock import Mock, MagicMock, patch, call
from pinnacle_dicom_converter.ui_nicegui.components.ct_viewer import CTViewer
from pinnacle_dicom_converter.core.models.image_set import ImageSet


@pytest.fixture
def mock_image_set():
    """Create a mock ImageSet with pixel data."""
    image_set = Mock(spec=ImageSet)
    # Create realistic CT data (512x512x100)
    image_set.pixel_data = np.random.randint(-1000, 3000, size=(100, 512, 512), dtype=np.int16)
    image_set.x_dim = 512
    image_set.y_dim = 512
    image_set.z_dim = 100
    image_set.x_pixdim = 0.1  # cm
    image_set.y_pixdim = 0.1  # cm
    image_set.z_pixdim = 0.3  # cm
    image_set.x_start = -25.6  # cm
    image_set.y_start = -25.6  # cm
    image_set.z_start = -30.0  # cm
    return image_set


@pytest.fixture
def ct_viewer():
    """Create a CTViewer instance for testing."""
    with patch.object(CTViewer, 'on'):
        viewer = CTViewer(
            initial_slice_index=50,
            initial_orientation='axial',
            initial_window_width=1400,
            initial_window_level=1000
        )
        # Mock the run_method to avoid actual communication with Vue component
        viewer.run_method = Mock()
        return viewer


class TestCTViewerInitialization:
    """Test CT Viewer initialization and configuration."""

    def test_default_initialization(self):
        """Test CT Viewer initializes with default parameters."""
        with patch.object(CTViewer, 'on'):
            viewer = CTViewer()

            assert viewer._props['initialSliceIndex'] == 0
            assert viewer._props['initialOrientation'] == 'axial'
            assert viewer._props['initialWindowWidth'] == 1400
            assert viewer._props['initialWindowLevel'] == 1000

    def test_custom_initialization(self):
        """Test CT Viewer initializes with custom parameters."""
        with patch.object(CTViewer, 'on'):
            viewer = CTViewer(
                initial_slice_index=25,
                initial_orientation='sagittal',
                initial_window_width=2000,
                initial_window_level=500
            )

            assert viewer._props['initialSliceIndex'] == 25
            assert viewer._props['initialOrientation'] == 'sagittal'
            assert viewer._props['initialWindowWidth'] == 2000
            assert viewer._props['initialWindowLevel'] == 500

    def test_event_handler_registration(self):
        """Test that event handlers are registered during initialization."""
        mock_on = Mock()
        with patch.object(CTViewer, 'on', mock_on):
            viewer = CTViewer()

            # Verify event handlers were registered
            assert mock_on.call_count == 3
            mock_on.assert_any_call('slice_changed', viewer._handle_slice_changed)
            mock_on.assert_any_call('mouse_move', viewer._handle_mouse_move)
            mock_on.assert_any_call('data_loaded', viewer._handle_data_loaded)

    def test_initial_state(self):
        """Test initial state of CT Viewer."""
        with patch.object(CTViewer, 'on'):
            viewer = CTViewer()

            assert viewer.image_set is None
            assert viewer._dimensions == (0, 0, 0)
            assert viewer.on_slice_changed is None
            assert viewer.on_mouse_move is None
            assert viewer.on_data_loaded is None


class TestCTViewerDataLoading:
    """Test CT Viewer data loading functionality."""

    def test_load_image_set_success(self, ct_viewer, mock_image_set):
        """Test successful image set loading."""
        ct_viewer.load_image_set(mock_image_set)

        assert ct_viewer.image_set == mock_image_set
        assert ct_viewer._dimensions == (100, 512, 512)

        # Verify run_method was called with correct parameters
        ct_viewer.run_method.assert_called_once()
        call_args = ct_viewer.run_method.call_args
        assert call_args[0][0] == 'loadImageData'

        # Verify dimensions dict
        dimensions = call_args[0][2]
        assert dimensions['x'] == 512
        assert dimensions['y'] == 512
        assert dimensions['z'] == 100

    def test_load_image_set_converts_to_float32(self, ct_viewer, mock_image_set):
        """Test that image data is converted to float32."""
        # Set pixel data as int16
        mock_image_set.pixel_data = np.array([[[1, 2], [3, 4]]], dtype=np.int16)
        mock_image_set.x_dim = 2
        mock_image_set.y_dim = 2
        mock_image_set.z_dim = 1

        ct_viewer.load_image_set(mock_image_set)

        # Verify data was converted (check in the call)
        call_args = ct_viewer.run_method.call_args
        array_buffer = call_args[0][1]
        # tobytes() should have been called on float32 array
        assert isinstance(array_buffer, bytes)

    def test_load_image_set_spacing_conversion(self, ct_viewer, mock_image_set):
        """Test that spacing is converted from cm to mm."""
        ct_viewer.load_image_set(mock_image_set)

        call_args = ct_viewer.run_method.call_args
        spacing = call_args[0][3]

        # cm to mm conversion (multiply by 10)
        assert spacing['x'] == pytest.approx(1.0)  # 0.1 cm * 10
        assert spacing['y'] == pytest.approx(1.0)
        assert spacing['z'] == pytest.approx(3.0)

    def test_load_image_set_origin_conversion(self, ct_viewer, mock_image_set):
        """Test that origin is converted from cm to mm."""
        ct_viewer.load_image_set(mock_image_set)

        call_args = ct_viewer.run_method.call_args
        origin = call_args[0][4]

        # cm to mm conversion (multiply by 10)
        assert origin['x'] == pytest.approx(-256.0)  # -25.6 cm * 10
        assert origin['y'] == pytest.approx(-256.0)
        assert origin['z'] == pytest.approx(-300.0)

    def test_load_image_set_missing_pixel_data(self, ct_viewer):
        """Test that loading image set without pixel_data raises ValueError."""
        mock_image_set = Mock(spec=ImageSet)
        mock_image_set.pixel_data = None

        with pytest.raises(ValueError, match="ImageSet must have pixel_data attribute"):
            ct_viewer.load_image_set(mock_image_set)

    def test_load_image_set_no_pixel_data_attribute(self, ct_viewer):
        """Test that loading image set without pixel_data attribute raises ValueError."""
        mock_image_set = Mock(spec=ImageSet)
        del mock_image_set.pixel_data

        with pytest.raises(ValueError, match="ImageSet must have pixel_data attribute"):
            ct_viewer.load_image_set(mock_image_set)


class TestCTViewerNavigation:
    """Test CT Viewer navigation methods."""

    def test_navigate_slice_forward(self, ct_viewer):
        """Test navigating forward by slice delta."""
        ct_viewer.navigate_slice(5)

        ct_viewer.run_method.assert_called_once_with('navigateSlice', 5)

    def test_navigate_slice_backward(self, ct_viewer):
        """Test navigating backward by slice delta."""
        ct_viewer.navigate_slice(-3)

        ct_viewer.run_method.assert_called_once_with('navigateSlice', -3)

    def test_navigate_slice_single(self, ct_viewer):
        """Test navigating by single slice."""
        ct_viewer.navigate_slice(1)

        ct_viewer.run_method.assert_called_once_with('navigateSlice', 1)


class TestCTViewerOrientation:
    """Test CT Viewer orientation methods."""

    def test_set_orientation_axial(self, ct_viewer):
        """Test setting axial orientation."""
        ct_viewer.set_orientation('axial')

        ct_viewer.run_method.assert_called_once_with('setOrientation', 'axial')

    def test_set_orientation_sagittal(self, ct_viewer):
        """Test setting sagittal orientation."""
        ct_viewer.set_orientation('sagittal')

        ct_viewer.run_method.assert_called_once_with('setOrientation', 'sagittal')

    def test_set_orientation_coronal(self, ct_viewer):
        """Test setting coronal orientation."""
        ct_viewer.set_orientation('coronal')

        ct_viewer.run_method.assert_called_once_with('setOrientation', 'coronal')

    def test_set_orientation_invalid(self, ct_viewer):
        """Test that invalid orientation raises ValueError."""
        with pytest.raises(ValueError, match="Invalid orientation"):
            ct_viewer.set_orientation('invalid')

    def test_set_orientation_case_sensitive(self, ct_viewer):
        """Test that orientation is case-sensitive."""
        with pytest.raises(ValueError, match="Invalid orientation"):
            ct_viewer.set_orientation('AXIAL')


class TestCTViewerWindowLevel:
    """Test CT Viewer window/level methods."""

    def test_set_window_level(self, ct_viewer):
        """Test setting window/level."""
        ct_viewer.set_window_level(2000, 500)

        ct_viewer.run_method.assert_called_once_with('setWindowLevel', 2000, 500)

    def test_set_window_level_lung_preset(self, ct_viewer):
        """Test setting lung window/level preset."""
        ct_viewer.set_window_level(1500, -600)

        ct_viewer.run_method.assert_called_once_with('setWindowLevel', 1500, -600)

    def test_set_window_level_bone_preset(self, ct_viewer):
        """Test setting bone window/level preset."""
        ct_viewer.set_window_level(2500, 300)

        ct_viewer.run_method.assert_called_once_with('setWindowLevel', 2500, 300)


class TestCTViewerZoom:
    """Test CT Viewer zoom methods."""

    def test_set_zoom_100_percent(self, ct_viewer):
        """Test setting zoom to 100%."""
        with patch.object(ct_viewer, 'update'):
            ct_viewer.set_zoom(1.0)

            assert ct_viewer._props['zoomFactor'] == 1.0
            ct_viewer.update.assert_called_once()

    def test_set_zoom_200_percent(self, ct_viewer):
        """Test setting zoom to 200%."""
        with patch.object(ct_viewer, 'update'):
            ct_viewer.set_zoom(2.0)

            assert ct_viewer._props['zoomFactor'] == 2.0
            ct_viewer.update.assert_called_once()

    def test_set_zoom_50_percent(self, ct_viewer):
        """Test setting zoom to 50%."""
        with patch.object(ct_viewer, 'update'):
            ct_viewer.set_zoom(0.5)

            assert ct_viewer._props['zoomFactor'] == 0.5
            ct_viewer.update.assert_called_once()


class TestCTViewerSVGOverlay:
    """Test CT Viewer SVG overlay methods."""

    def test_update_svg_overlay(self, ct_viewer):
        """Test updating SVG overlay content."""
        svg_content = '<circle cx="100" cy="100" r="50" fill="red"/>'
        ct_viewer.update_svg_overlay(svg_content)

        ct_viewer.run_method.assert_called_once_with('updateSVGOverlay', svg_content)

    def test_update_svg_overlay_complex(self, ct_viewer):
        """Test updating SVG overlay with complex content."""
        svg_content = '''
        <g id="roi-1">
            <path d="M 10,10 L 100,10 L 100,100 Z" fill="rgba(255,0,0,0.3)"/>
            <text x="50" y="50">ROI 1</text>
        </g>
        '''
        ct_viewer.update_svg_overlay(svg_content)

        ct_viewer.run_method.assert_called_once_with('updateSVGOverlay', svg_content)

    def test_clear_svg_overlay(self, ct_viewer):
        """Test clearing SVG overlay."""
        ct_viewer.clear_svg_overlay()

        ct_viewer.run_method.assert_called_once_with('clearSVGOverlay')


class TestCTViewerPreloading:
    """Test CT Viewer preloading methods."""

    def test_preload_adjacent_slices_default(self, ct_viewer):
        """Test preloading adjacent slices with default range."""
        ct_viewer.preload_adjacent_slices()

        ct_viewer.run_method.assert_called_once_with('preloadAdjacentSlices', 5)

    def test_preload_adjacent_slices_custom_range(self, ct_viewer):
        """Test preloading adjacent slices with custom range."""
        ct_viewer.preload_adjacent_slices(range=10)

        ct_viewer.run_method.assert_called_once_with('preloadAdjacentSlices', 10)

    def test_preload_adjacent_slices_small_range(self, ct_viewer):
        """Test preloading adjacent slices with small range."""
        ct_viewer.preload_adjacent_slices(range=2)

        ct_viewer.run_method.assert_called_once_with('preloadAdjacentSlices', 2)


class TestCTViewerEventHandlers:
    """Test CT Viewer event handling."""

    def test_handle_slice_changed_no_callback(self, ct_viewer):
        """Test slice changed event with no callback registered."""
        mock_event = Mock()
        mock_event.args = {'index': 25, 'orientation': 'axial'}

        # Should not raise exception
        ct_viewer._handle_slice_changed(mock_event)

    def test_handle_slice_changed_with_callback(self, ct_viewer):
        """Test slice changed event with callback registered."""
        callback = Mock()
        ct_viewer.on_slice_changed = callback

        mock_event = Mock()
        mock_event.args = {'index': 25, 'orientation': 'axial'}

        ct_viewer._handle_slice_changed(mock_event)

        callback.assert_called_once_with(25, 'axial')

    def test_handle_mouse_move_no_callback(self, ct_viewer):
        """Test mouse move event with no callback registered."""
        mock_event = Mock()
        mock_event.args = {
            'image': {'x': 256, 'y': 256},
            'world': {'x': 0.0, 'y': 0.0, 'z': 150.0}
        }

        # Should not raise exception
        ct_viewer._handle_mouse_move(mock_event)

    def test_handle_mouse_move_with_callback(self, ct_viewer):
        """Test mouse move event with callback registered."""
        callback = Mock()
        ct_viewer.on_mouse_move = callback

        mock_event = Mock()
        mock_event.args = {
            'image': {'x': 256, 'y': 256},
            'world': {'x': 0.0, 'y': 0.0, 'z': 150.0}
        }

        ct_viewer._handle_mouse_move(mock_event)

        callback.assert_called_once_with(
            {'x': 256, 'y': 256},
            {'x': 0.0, 'y': 0.0, 'z': 150.0}
        )

    def test_handle_data_loaded_no_callback(self, ct_viewer):
        """Test data loaded event with no callback registered."""
        mock_event = Mock()
        mock_event.args = {
            'dimensions': {'x': 512, 'y': 512, 'z': 100},
            'spacing': {'x': 1.0, 'y': 1.0, 'z': 3.0}
        }

        # Should not raise exception
        ct_viewer._handle_data_loaded(mock_event)

    def test_handle_data_loaded_with_callback(self, ct_viewer):
        """Test data loaded event with callback registered."""
        callback = Mock()
        ct_viewer.on_data_loaded = callback

        mock_event = Mock()
        mock_event.args = {
            'dimensions': {'x': 512, 'y': 512, 'z': 100},
            'spacing': {'x': 1.0, 'y': 1.0, 'z': 3.0}
        }

        ct_viewer._handle_data_loaded(mock_event)

        expected_args = {
            'dimensions': {'x': 512, 'y': 512, 'z': 100},
            'spacing': {'x': 1.0, 'y': 1.0, 'z': 3.0}
        }
        callback.assert_called_once_with(expected_args)


class TestCTViewerEdgeCases:
    """Test CT Viewer edge cases and error handling."""

    def test_load_very_large_dataset(self, ct_viewer):
        """Test loading very large CT dataset (1024x1024x500)."""
        large_image_set = Mock(spec=ImageSet)
        large_image_set.pixel_data = np.random.randint(
            -1000, 3000, size=(500, 1024, 1024), dtype=np.int16
        )
        large_image_set.x_dim = 1024
        large_image_set.y_dim = 1024
        large_image_set.z_dim = 500
        large_image_set.x_pixdim = 0.05
        large_image_set.y_pixdim = 0.05
        large_image_set.z_pixdim = 0.2
        large_image_set.x_start = -25.6
        large_image_set.y_start = -25.6
        large_image_set.z_start = -50.0

        # Should not raise exception
        ct_viewer.load_image_set(large_image_set)

        assert ct_viewer._dimensions == (500, 1024, 1024)

    def test_load_small_dataset(self, ct_viewer):
        """Test loading small CT dataset (128x128x10)."""
        small_image_set = Mock(spec=ImageSet)
        small_image_set.pixel_data = np.random.randint(
            -1000, 3000, size=(10, 128, 128), dtype=np.int16
        )
        small_image_set.x_dim = 128
        small_image_set.y_dim = 128
        small_image_set.z_dim = 10
        small_image_set.x_pixdim = 0.2
        small_image_set.y_pixdim = 0.2
        small_image_set.z_pixdim = 0.5
        small_image_set.x_start = -12.8
        small_image_set.y_start = -12.8
        small_image_set.z_start = -2.5

        # Should not raise exception
        ct_viewer.load_image_set(small_image_set)

        assert ct_viewer._dimensions == (10, 128, 128)

    def test_multiple_loads(self, ct_viewer, mock_image_set):
        """Test loading multiple image sets sequentially."""
        # Load first image set
        ct_viewer.load_image_set(mock_image_set)
        assert ct_viewer.image_set == mock_image_set

        # Load second image set
        second_image_set = Mock(spec=ImageSet)
        second_image_set.pixel_data = np.random.randint(
            -1000, 3000, size=(50, 256, 256), dtype=np.int16
        )
        second_image_set.x_dim = 256
        second_image_set.y_dim = 256
        second_image_set.z_dim = 50
        second_image_set.x_pixdim = 0.15
        second_image_set.y_pixdim = 0.15
        second_image_set.z_pixdim = 0.4
        second_image_set.x_start = -19.2
        second_image_set.y_start = -19.2
        second_image_set.z_start = -10.0

        ct_viewer.load_image_set(second_image_set)

        assert ct_viewer.image_set == second_image_set
        assert ct_viewer._dimensions == (50, 256, 256)

    def test_extreme_hounsfield_values(self, ct_viewer):
        """Test handling extreme Hounsfield unit values."""
        extreme_image_set = Mock(spec=ImageSet)
        # Extreme HU values: air=-1000, bone=+3000
        extreme_image_set.pixel_data = np.array([
            [[-1024, -1000, 0], [1000, 2000, 3095]],
            [[-500, 0, 500], [1500, 2500, 3000]]
        ], dtype=np.int16)
        extreme_image_set.x_dim = 3
        extreme_image_set.y_dim = 2
        extreme_image_set.z_dim = 2
        extreme_image_set.x_pixdim = 0.1
        extreme_image_set.y_pixdim = 0.1
        extreme_image_set.z_pixdim = 0.3
        extreme_image_set.x_start = 0.0
        extreme_image_set.y_start = 0.0
        extreme_image_set.z_start = 0.0

        # Should handle extreme values without error
        ct_viewer.load_image_set(extreme_image_set)

        assert ct_viewer._dimensions == (2, 2, 3)
